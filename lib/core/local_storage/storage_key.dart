enum StorageKey {
  /// theme mode
  /// stored type is [int] ,ThemeMode index
  themeMode,

  /// language id
  /// stored type is [int], Language model id
  languageId,

  /// onboarding completion status
  /// stored type is [bool]
  onboardingCompleted,

  /// landing page seen status
  /// stored type is [bool]
  landingPageSeen,

  /// selected user type
  /// stored type is [String], UserType enum name
  selectedUserType,
}
