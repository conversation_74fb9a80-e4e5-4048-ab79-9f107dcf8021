import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../src/auth/presentation/auth_page_view.dart';
import '../../src/auth/presentation/forgot_password_screen.dart';
import '../../src/auth/presentation/reset_password_screen.dart';
import '../../src/home/<USER>/home_page_view.dart';
import '../../start/splash/splash_view.dart';
import '../../start/onboarding/presentation/landing_view.dart';
import '../../start/onboarding/presentation/onboarding_view.dart';
import '../../src/instructor/presentation/instructor_main_view.dart';
import '../../src/student/presentation/student_main_view.dart';
import '../../src/student/presentation/course_list_view.dart';
import '../../src/student/presentation/trainer_detail_view.dart';
import '../../src/profile_form/presentation/profile_form_screen.dart';
import '../../src/profile_form/presentation/form_approval_waiting_screen.dart';
import '../../src/dashboard/presentation/dashboard_screen.dart';

/// App router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    routes: [
      GoRoute(path: '/splash', builder: (context, state) => const SplashView()),
      GoRoute(
        path: '/landing',
        builder: (context, state) => const LandingView(),
      ),
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingView(),
      ),
      GoRoute(path: '/auth', builder: (context, state) => const AuthPageView()),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/reset-password',
        builder: (context, state) {
          final email = state.uri.queryParameters['email'] ?? '';
          final accessToken = state.uri.queryParameters['access_token'];
          final refreshToken = state.uri.queryParameters['refresh_token'];
          return ResetPasswordScreen(
            email: email,
            accessToken: accessToken,
            refreshToken: refreshToken,
          );
        },
      ),
      GoRoute(path: '/home', builder: (context, state) => const HomePageView()),
      GoRoute(
        path: '/instructor-main',
        builder: (context, state) => const InstructorMainView(),
      ),
      GoRoute(
        path: '/instructor-homepage',
        builder: (context, state) => const InstructorMainView(),
      ),
      GoRoute(
        path: '/student-main',
        builder: (context, state) => const StudentMainView(),
      ),
      GoRoute(
        path: '/course-list',
        builder: (context, state) => const CourseListView(),
      ),
      GoRoute(
        path: '/trainer-detail/:trainerId',
        builder: (context, state) {
          final trainerId = state.pathParameters['trainerId']!;
          return TrainerDetailView(instructorId: trainerId);
        },
      ),
      GoRoute(
        path: '/profile-form',
        builder: (context, state) => const ProfileFormScreen(),
      ),
      GoRoute(
        path: '/form-approval-waiting',
        builder: (context, state) => const FormApprovalWaitingScreen(),
      ),
      GoRoute(
        path: '/dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),
    ],
  );
});
