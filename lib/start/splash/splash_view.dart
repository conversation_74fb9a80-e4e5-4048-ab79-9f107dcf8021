import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:fitgo_app/src/dashboard/application/dashboard_provider.dart';
import 'package:fitgo_app/src/enrollment/application/enrollment_provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SplashView extends HookConsumerWidget {
  const SplashView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to auth state and onboarding completion
    ref.listen<bool>(isAuthenticatedProvider, (previous, next) {
      if (next) {
        // User is authenticated, go to home
        context.go('/home');
      }
    });

    ref.listen<bool>(onboardingCompletedProvider, (previous, next) {
      if (next && !ref.read(isAuthenticatedProvider)) {
        // Onboarding completed but not authenticated, go to auth
        context.go('/auth');
      }
    });

    // Auto-navigate after splash delay
    Future.delayed(const Duration(seconds: 2), () async {
      if (context.mounted) {
        final isAuthenticated = ref.read(isAuthenticatedProvider);
        final onboardingCompleted = ref.read(onboardingCompletedProvider);
        final userType = ref.read(currentUserTypeProvider);

        if (isAuthenticated) {
          final currentUser = Supabase.instance.client.auth.currentUser;

          if (currentUser != null) {
            // FIRST: Check user role from database to determine correct flow
            bool isInstructor = false;
            try {
              final enrollmentRepository = ref.read(
                enrollmentRepositoryProvider,
              );
              isInstructor = await enrollmentRepository.isUserInstructor(
                currentUser.id,
              );

              // Update the user type provider with the correct role
              if (isInstructor) {
                ref.read(currentUserTypeProvider.notifier).state =
                    UserType.instructor;
                debugPrint(
                  '🎯 User role detected: INSTRUCTOR - navigating to instructor homepage',
                );
              } else {
                ref.read(currentUserTypeProvider.notifier).state =
                    UserType.student;
                debugPrint(
                  '🎯 User role detected: STUDENT - navigating to student flow',
                );
              }
            } catch (e) {
              debugPrint('❌ Error checking user role: $e');
              // Default to student if error
              isInstructor = false;
            }

            if (!context.mounted) return;

            // Navigate based on actual user role
            if (isInstructor) {
              // User is instructor, go to instructor homepage
              debugPrint('🚀 Navigating instructor to: /instructor-main');
              context.go('/instructor-main');
              return;
            } else {
              // User is student, check enrollment and onboarding status

              // Check if user has any active enrollments
              final enrollmentRepository = ref.read(
                enrollmentRepositoryProvider,
              );
              final enrollmentResult = await enrollmentRepository
                  .checkUserEnrollment(currentUser.id);

              if (!context.mounted) return;

              if (enrollmentResult.error != null) {
                debugPrint(
                  '❌ Error checking enrollment: ${enrollmentResult.error}',
                );
                // On error, redirect to course list as fallback
                context.go('/course-list');
                return;
              }

              if (!enrollmentResult.hasActiveEnrollment) {
                // No active enrollment found, redirect to course list
                context.go('/course-list');
                return;
              }

              // User has active enrollment, check onboarding completion status
              final dashboardRepository = ref.read(dashboardRepositoryProvider);
              final onboardingStatus =
                  await dashboardRepository.getOnboardingStatus();

              if (!context.mounted) return;

              if (!onboardingStatus.hasCompletedProfileForm) {
                // Profile form not submitted, show profile form first
                context.go('/profile-form');
                return;
              }

              if (onboardingStatus.shouldShowDashboard) {
                // Both payment and profile form completed, show dashboard
                context.go('/dashboard');
                return;
              }

              // Fallback for students
              context.go('/course-list');
              return;
            }
          }

          // Fallback navigation based on stored user type (should not reach here normally)
          if (userType == UserType.instructor) {
            context.go('/instructor-main');
          } else {
            context.go('/course-list');
          }
        } else if (onboardingCompleted) {
          context.go('/auth');
        } else if (userType != null) {
          // User has selected type but hasn't completed onboarding
          context.go('/onboarding');
        } else {
          // First time user, show landing page
          context.go('/landing');
        }
      }
    });

    return Container(
      decoration: const BoxDecoration(color: AColor.black),
      child: Center(
        child: AImage(imgPath: APath.appLogo, width: context.width / 2),
      ),
    );
  }
}
