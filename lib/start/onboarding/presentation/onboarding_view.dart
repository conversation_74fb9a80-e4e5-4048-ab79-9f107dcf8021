import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/app_bar_back_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/flat_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/start/onboarding/application/onboarding_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

class OnboardingView extends HookConsumerWidget {
  const OnboardingView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pageController = usePageController();
    final currentPage = ref.watch(currentOnboardingPageProvider);
    final onboardingItems = ref.watch(onboardingItemsProvider);
    final isLastPage = ref.watch(isLastOnboardingPageProvider);

    return OnboardingScaffold(
      topBar: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (currentPage > 0)
            AppBarBackButton(
              onTap: () {
                pageController.previousPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
            ),
        ],
      ),
      bottomBar: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              onboardingItems.length,
              (i) => _PageIndicator(isActive: i == currentPage),
            ),
          ),
          const SizedBox(height: 32),
          RaisedButtonWidget(
            width: double.infinity,
            text: isLastPage ? 'Start'.hardcoded : 'Continue'.hardcoded,
            borderRadius: 15,
            fontColor: AColor.buttonTextColor,
            bgColor: AColor.buttonColor,
            fontStyle: ATextStyle.buttonText,
            borderSide: BorderSide(color: AColor.textSecondaryColor, width: 2),
            onPressed: () {
              if (isLastPage) {
                ref.read(onboardingCompletedProvider.notifier).state = true;
                context.go('/auth');
              } else {
                pageController.nextPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              }
            },
          ),
          if (!isLastPage) ...[
            const SizedBox(height: 8),
            FlatButtonWidget(
              text: 'Skip'.hardcoded,
              onPressed: () {
                ref.read(onboardingCompletedProvider.notifier).state = true;
                context.go('/auth');
              },
            ),
          ],
        ],
      ),
      child: PageView.builder(
        controller: pageController,
        onPageChanged:
            (page) =>
                ref.read(currentOnboardingPageProvider.notifier).state = page,
        itemCount: onboardingItems.length,
        itemBuilder: (context, index) {
          final item = onboardingItems[index];
          return _OnboardingPageItem(item: item);
        },
      ),
    );
  }
}

class _OnboardingPageItem extends StatelessWidget {
  final dynamic item;
  const _OnboardingPageItem({required this.item});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: 16,
        children: [
          Expanded(
            flex: 3,
            child: AImage(imgPath: item.imgPath, fit: BoxFit.contain),
          ),
          Expanded(
            flex: 3,
            child: Column(
              spacing: 16,
              children: [
                TextWidget(
                  item.title,
                  textAlign: TextAlign.center,
                  style: ATextStyle.title,
                ),
                TextWidget(
                  item.desc,
                  textAlign: TextAlign.center,
                  style: ATextStyle.description,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _PageIndicator extends StatelessWidget {
  final bool isActive;
  const _PageIndicator({required this.isActive});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: isActive ? AColor.white : AColor.white.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
