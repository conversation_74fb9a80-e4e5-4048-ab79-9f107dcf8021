/* import 'package:fitgo_app/src/shared/constants/assets.gen.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/student/presentation/student_exercise_tab.dart';

class StudentMainView extends HookConsumerWidget {
  const StudentMainView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
     final currentTab = useState(0);
     
   return Scaffold(
      backgroundColor: AColor.black,
      body: IndexedStack(
        index: currentTab.value,
        children: const [
          StudentExerciseTab(),
          StudentNutritionTab(),
          StudentProgressTab(),
          StudentExploreTab(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AColor.black,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(
                  currentTab,
                  0,
                  Assets.icons.bottomExerciseIcon.path,
                  'Exercise',
                ),
                _buildNavItem(
                  currentTab,
                  1,
                  Assets.icons.bottomNutritionIcon.path,
                  'Nutrition',
                ),
                _buildNavItem(
                  currentTab,
                  2,
                  Assets.icons.bottomProgressIcon.path,
                  'Progress',
                ),
                _buildNavItem(
                  currentTab,
                  3,
                  Assets.icons.bottomExploreIcon.path,
                  'Explore',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// StudentExerciseTab is now imported from student_exercise_tab.dart
Widget _buildNavItem(
  ValueNotifier<int> currentTab,
  int index,
  String iconPath,
  String label,
) {
  final isSelected = currentTab.value == index;

  return Expanded(
    child: GestureDetector(
      onTap: () => currentTab.value = index,
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon with color change when selected
            AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              curve: Curves.easeInOut,
              width: 24,
              height: 24,
              child: AImage(
                imgPath: iconPath,
                width: 24,
                height: 24,
                color:
                    isSelected
                        ? AColor
                            .buttonColor // Green when selected
                        : AColor.grey, // Gray when not selected
              ),
            ),

            const SizedBox(height: 4),

            // Label with green color when selected
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 250),
              curve: Curves.easeInOut,
              style: TextStyle(
                color:
                    isSelected
                        ? AColor
                            .buttonColor // Green when selected
                        : AColor.grey, // Gray when not selected
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 11,
              ),
              child: Text(label),
            ),
          ],
        ),
      ),
    ),
  );
}

// Nutrition Tab for Students
class StudentNutritionTab extends StatelessWidget {
  const StudentNutritionTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Today\'s Nutrition'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildMacroCard(
                      'Calories',
                      '1,850',
                      '2,200',
                      Colors.orange,
                    ),
                    _buildMacroCard('Protein', '120g', '150g', Colors.red),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildMacroCard('Carbs', '180g', '220g', Colors.blue),
                    _buildMacroCard('Fat', '65g', '80g', Colors.green),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Meal Plan'.hardcoded,
            style: ATextStyle.large.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView(
              children: [
                _buildMealCard('Breakfast', 'Oatmeal with berries', '350 cal'),
                _buildMealCard('Lunch', 'Grilled chicken salad', '450 cal'),
                _buildMealCard('Snack', 'Greek yogurt', '150 cal'),
                _buildMealCard('Dinner', 'Salmon with vegetables', '500 cal'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMacroCard(
    String title,
    String current,
    String target,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            TextWidget(
              title,
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
            const SizedBox(height: 4),
            TextWidget(
              current,
              style: ATextStyle.medium.copyWith(color: color),
            ),
            TextWidget(
              '/ $target',
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMealCard(String mealType, String description, String calories) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AColor.bottomSheetBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AColor.textSecondaryColor),
      ),
      child: Row(
        children: [
          Icon(Icons.restaurant, color: AColor.buttonColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  mealType,
                  style: ATextStyle.medium.copyWith(color: AColor.white),
                ),
                TextWidget(
                  description,
                  style: ATextStyle.small.copyWith(color: AColor.grey),
                ),
              ],
            ),
          ),
          TextWidget(
            calories,
            style: ATextStyle.small.copyWith(color: AColor.buttonColor),
          ),
        ],
      ),
    );
  }
}

// Progress Tab for Students
class StudentProgressTab extends StatelessWidget {
  const StudentProgressTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Your Progress'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildProgressCard('Weight', '75.2 kg', '-2.3 kg'),
                    _buildProgressCard('BMI', '22.1', '-0.8'),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildProgressCard('Workouts', '24', '+6'),
                    _buildProgressCard('Streak', '12 days', '+5'),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Recent Activities'.hardcoded,
            style: ATextStyle.large.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: 5,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AColor.bottomSheetBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AColor.textSecondaryColor),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: AColor.buttonColor),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              'Upper Body Workout',
                              style: ATextStyle.medium.copyWith(
                                color: AColor.white,
                              ),
                            ),
                            TextWidget(
                              '${index + 1} days ago • 45 min',
                              style: ATextStyle.small.copyWith(
                                color: AColor.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      TextWidget(
                        '320 cal',
                        style: ATextStyle.small.copyWith(
                          color: AColor.buttonColor,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard(String title, String value, String change) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: AColor.black,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AColor.textSecondaryColor),
        ),
        child: Column(
          children: [
            TextWidget(
              title,
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
            const SizedBox(height: 4),
            TextWidget(
              value,
              style: ATextStyle.medium.copyWith(color: AColor.white),
            ),
            TextWidget(
              change,
              style: ATextStyle.small.copyWith(color: AColor.buttonColor),
            ),
          ],
        ),
      ),
    );
  }
}

// Explore Tab for Students
class StudentExploreTab extends HookConsumerWidget {
  const StudentExploreTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Explore'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Discover new workouts, nutrition tips, and connect with trainers.',
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
          const SizedBox(height: 20),

          // Find Trainers Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person_search,
                      color: AColor.buttonColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    TextWidget(
                      'Find Personal Trainers',
                      style: ATextStyle.large.copyWith(color: AColor.white),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                TextWidget(
                  'Browse and connect with certified personal trainers in your area.',
                  style: ATextStyle.medium.copyWith(color: AColor.grey),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      context.push('/course-list');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AColor.buttonColor,
                      foregroundColor: AColor.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: TextWidget(
                      'Browse Trainers',
                      style: ATextStyle.medium.copyWith(color: AColor.white),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Logout Button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.logout, color: Colors.red, size: 24),
                    const SizedBox(width: 12),
                    TextWidget(
                      'Account Settings',
                      style: ATextStyle.large.copyWith(color: AColor.white),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                TextWidget(
                  'Manage your account and logout.',
                  style: ATextStyle.medium.copyWith(color: AColor.grey),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      // Show confirmation dialog
                      final shouldLogout = await showDialog<bool>(
                        context: context,
                        builder:
                            (context) => AlertDialog(
                              backgroundColor:
                                  AColor.bottomSheetBackgroundColor,
                              title: TextWidget(
                                'Logout',
                                style: ATextStyle.large.copyWith(
                                  color: AColor.white,
                                ),
                              ),
                              content: TextWidget(
                                'Are you sure you want to logout?',
                                style: ATextStyle.medium.copyWith(
                                  color: AColor.grey,
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(context).pop(false),
                                  child: TextWidget(
                                    'Cancel',
                                    style: ATextStyle.medium.copyWith(
                                      color: AColor.grey,
                                    ),
                                  ),
                                ),
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(context).pop(true),
                                  child: TextWidget(
                                    'Logout',
                                    style: ATextStyle.medium.copyWith(
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                      );

                      if (shouldLogout == true) {
                        try {
                          // Logout using auth repository
                          final authRepo = ref.read(authRepositoryProvider);
                          await authRepo.signOut();

                          if (context.mounted) {
                            // Navigate to auth page
                            context.go('/auth');
                          }
                        } catch (e) {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('Logout failed: $e')),
                            );
                          }
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: AColor.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: TextWidget(
                      'Logout',
                      style: ATextStyle.medium.copyWith(color: AColor.white),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          Expanded(
            child: Center(
              child: TextWidget(
                'More features coming soon!',
                style: ATextStyle.medium.copyWith(color: AColor.grey),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
 */

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/student/presentation/student_exercise_tab.dart';
import 'package:fitgo_app/src/student/presentation/student_nutrition_tab.dart';
import 'package:fitgo_app/src/shared/constants/assets.gen.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class StudentMainView extends HookConsumerWidget {
  const StudentMainView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTab = useState(0);

    return Scaffold(
      backgroundColor: AColor.black,
      body: IndexedStack(
        index: currentTab.value,
        children: const [
          StudentExerciseTab(),
          StudentNutritionTab(),
          StudentProgressTab(),
          StudentExploreTab(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AColor.black,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(
                  currentTab,
                  0,
                  Assets.icons.bottomExerciseIcon.path,
                  'Exercise',
                ),
                _buildNavItem(
                  currentTab,
                  1,
                  Assets.icons.bottomNutritionIcon.path,
                  'Nutrition',
                ),
                _buildNavItem(
                  currentTab,
                  2,
                  Assets.icons.bottomProgressIcon.path,
                  'Progress',
                ),
                _buildNavItem(
                  currentTab,
                  3,
                  Assets.icons.bottomExploreIcon.path,
                  'Explore',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    ValueNotifier<int> currentTab,
    int index,
    String iconPath,
    String label,
  ) {
    final isSelected = currentTab.value == index;

    return Expanded(
      child: GestureDetector(
        onTap: () => currentTab.value = index,
        behavior: HitTestBehavior.opaque,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon with color change when selected
              AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                width: 24,
                height: 24,
                child: AImage(
                  imgPath: iconPath,
                  width: 24,
                  height: 24,
                  color:
                      isSelected
                          ? AColor
                              .buttonColor // Green when selected
                          : AColor.grey, // Gray when not selected
                ),
              ),

              const SizedBox(height: 4),

              // Label with green color when selected
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOut,
                style: TextStyle(
                  color:
                      isSelected
                          ? AColor
                              .buttonColor // Green when selected
                          : AColor.grey, // Gray when not selected
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  fontSize: 11,
                ),
                child: Text(label),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Nutrition Tab for Students
class StudentNutritionTab extends StatelessWidget {
  const StudentNutritionTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Today\'s Nutrition'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildMacroCard(
                      'Calories',
                      '1,850',
                      '2,200',
                      Colors.orange,
                    ),
                    _buildMacroCard('Protein', '120g', '150g', Colors.red),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildMacroCard('Carbs', '180g', '220g', Colors.blue),
                    _buildMacroCard('Fat', '65g', '80g', Colors.green),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Meal Plan'.hardcoded,
            style: ATextStyle.large.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView(
              children: [
                _buildMealCard('Breakfast', 'Oatmeal with berries', '350 cal'),
                _buildMealCard('Lunch', 'Grilled chicken salad', '450 cal'),
                _buildMealCard('Snack', 'Greek yogurt', '150 cal'),
                _buildMealCard('Dinner', 'Salmon with vegetables', '500 cal'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMacroCard(
    String title,
    String current,
    String target,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            TextWidget(
              title,
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
            const SizedBox(height: 4),
            TextWidget(
              current,
              style: ATextStyle.medium.copyWith(color: color),
            ),
            TextWidget(
              '/ $target',
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMealCard(String mealType, String description, String calories) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AColor.bottomSheetBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AColor.textSecondaryColor),
      ),
      child: Row(
        children: [
          Icon(Icons.restaurant, color: AColor.buttonColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  mealType,
                  style: ATextStyle.medium.copyWith(color: AColor.white),
                ),
                TextWidget(
                  description,
                  style: ATextStyle.small.copyWith(color: AColor.grey),
                ),
              ],
            ),
          ),
          TextWidget(
            calories,
            style: ATextStyle.small.copyWith(color: AColor.buttonColor),
          ),
        ],
      ),
    );
  }
}

// Progress Tab for Students
class StudentProgressTab extends StatelessWidget {
  const StudentProgressTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Your Progress'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildProgressCard('Weight', '75.2 kg', '-2.3 kg'),
                    _buildProgressCard('BMI', '22.1', '-0.8'),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildProgressCard('Workouts', '24', '+6'),
                    _buildProgressCard('Streak', '12 days', '+5'),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Recent Activities'.hardcoded,
            style: ATextStyle.large.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: 5,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AColor.bottomSheetBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AColor.textSecondaryColor),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: AColor.buttonColor),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              'Upper Body Workout',
                              style: ATextStyle.medium.copyWith(
                                color: AColor.white,
                              ),
                            ),
                            TextWidget(
                              '${index + 1} days ago • 45 min',
                              style: ATextStyle.small.copyWith(
                                color: AColor.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      TextWidget(
                        '320 cal',
                        style: ATextStyle.small.copyWith(
                          color: AColor.buttonColor,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard(String title, String value, String change) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: AColor.black,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AColor.textSecondaryColor),
        ),
        child: Column(
          children: [
            TextWidget(
              title,
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
            const SizedBox(height: 4),
            TextWidget(
              value,
              style: ATextStyle.medium.copyWith(color: AColor.white),
            ),
            TextWidget(
              change,
              style: ATextStyle.small.copyWith(color: AColor.buttonColor),
            ),
          ],
        ),
      ),
    );
  }
}

// Explore Tab for Students
class StudentExploreTab extends HookConsumerWidget {
  const StudentExploreTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Explore'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Discover new workouts, nutrition tips, and connect with trainers.',
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
          const SizedBox(height: 20),

          // Find Trainers Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.person_search,
                      color: AColor.buttonColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    TextWidget(
                      'Find Personal Trainers',
                      style: ATextStyle.large.copyWith(color: AColor.white),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                TextWidget(
                  'Browse and connect with certified personal trainers in your area.',
                  style: ATextStyle.medium.copyWith(color: AColor.grey),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      context.push('/course-list');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AColor.buttonColor,
                      foregroundColor: AColor.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: TextWidget(
                      'Browse Trainers',
                      style: ATextStyle.medium.copyWith(color: AColor.white),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Logout Button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.logout, color: Colors.red, size: 24),
                    const SizedBox(width: 12),
                    TextWidget(
                      'Account Settings',
                      style: ATextStyle.large.copyWith(color: AColor.white),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                TextWidget(
                  'Manage your account and logout.',
                  style: ATextStyle.medium.copyWith(color: AColor.grey),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () async {
                      // Show confirmation dialog
                      final shouldLogout = await showDialog<bool>(
                        context: context,
                        builder:
                            (context) => AlertDialog(
                              backgroundColor:
                                  AColor.bottomSheetBackgroundColor,
                              title: TextWidget(
                                'Logout',
                                style: ATextStyle.large.copyWith(
                                  color: AColor.white,
                                ),
                              ),
                              content: TextWidget(
                                'Are you sure you want to logout?',
                                style: ATextStyle.medium.copyWith(
                                  color: AColor.grey,
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(context).pop(false),
                                  child: TextWidget(
                                    'Cancel',
                                    style: ATextStyle.medium.copyWith(
                                      color: AColor.grey,
                                    ),
                                  ),
                                ),
                                TextButton(
                                  onPressed:
                                      () => Navigator.of(context).pop(true),
                                  child: TextWidget(
                                    'Logout',
                                    style: ATextStyle.medium.copyWith(
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                      );

                      if (shouldLogout == true) {
                        try {
                          // Logout using auth repository
                          final authRepo = ref.read(authRepositoryProvider);
                          await authRepo.signOut();

                          if (context.mounted) {
                            // Navigate to auth page
                            context.go('/auth');
                          }
                        } catch (e) {
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('Logout failed: $e')),
                            );
                          }
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: AColor.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: TextWidget(
                      'Logout',
                      style: ATextStyle.medium.copyWith(color: AColor.white),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          Expanded(
            child: Center(
              child: TextWidget(
                'More features coming soon!',
                style: ATextStyle.medium.copyWith(color: AColor.grey),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
