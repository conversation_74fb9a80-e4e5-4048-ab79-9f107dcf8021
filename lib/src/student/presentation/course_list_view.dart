import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/core/constants/app_constants.dart';
import 'package:fitgo_app/src/student/presentation/trainer_detail_view.dart';
import 'package:fitgo_app/src/student/application/trainer_list_provider.dart';
import 'package:fitgo_app/src/student/domain/trainer_list.dart';
import 'package:fitgo_app/src/shared/widgets/app_drawer.dart';

class CourseListView extends HookConsumerWidget {
  const CourseListView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final trainerListState = ref.watch(trainerListProvider);
    final searchController = useTextEditingController();
    final debounceTimer = useRef<Timer?>(null);
    final isSearching = useState(false);

    // Listen to search controller changes to update UI
    useEffect(() {
      void listener() {
        isSearching.value = searchController.text.isNotEmpty;
      }

      searchController.addListener(listener);
      return () => searchController.removeListener(listener);
    }, [searchController]);

    // Clean up timer on dispose
    useEffect(() {
      return () {
        debounceTimer.value?.cancel();
      };
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF111827), // Dark theme background
      endDrawer: const AppDrawer(),
      body: SafeArea(
        child: Column(
          children: [
            // Top section with logo and hamburger menu
            _buildTopSection(context),

            // Search bar
            _buildSearchBar(ref, searchController, debounceTimer, isSearching),

            // Filter tabs
            _buildFilterTabs(ref, trainerListState.filter),

            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Align(
                alignment: Alignment.centerLeft,
                child: TextWidget(
                  'Featured Instructors'.hardcoded,
                  style: ATextStyle.semiLarge.copyWith(
                    color: AColor.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            // Trainer list
            Expanded(child: _buildTrainerList(ref, trainerListState)),
          ],
        ),
      ),
    );
  }

  Widget _buildTopSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // App logo
          Expanded(
            child: TextWidget(
              AppConstants.appName,
              style: ATextStyle.title.copyWith(
                color: AColor.fitgoGreen,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Hamburger menu button
          Builder(
            builder:
                (context) => GestureDetector(
                  onTap: () => Scaffold.of(context).openEndDrawer(),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFF1F2937),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.menu,
                      color: AColor.textColor,
                      size: 20,
                    ),
                  ),
                ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF1F2937),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => FilterBottomSheet(ref: ref),
    );
  }

  Widget _buildSearchBar(
    WidgetRef ref,
    TextEditingController searchController,
    ObjectRef<Timer?> debounceTimer,
    ValueNotifier<bool> isSearching,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Filter button outside the search bar
          Builder(
            builder:
                (context) => GestureDetector(
                  onTap: () => _showFilterBottomSheet(context, ref),
                  child: Container(
                    height: 48,
                    width: 48,
                    decoration: BoxDecoration(
                      color: const Color(0xFF1F2937),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.tune,
                      color: AColor.textColor,
                      size: 20,
                    ),
                  ),
                ),
          ),
          const SizedBox(width: 12),
          // Search input field
          Expanded(
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF1F2937),
                borderRadius: BorderRadius.circular(12),
              ),
              child: ValueListenableBuilder<bool>(
                valueListenable: isSearching,
                builder:
                    (context, hasText, child) => TextField(
                      controller: searchController,
                      style: ATextStyle.medium.copyWith(
                        color: AColor.textColor,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Search instructors...'.hardcoded,
                        hintStyle: ATextStyle.medium.copyWith(
                          color: AColor.grey,
                        ),
                        prefixIcon: const Icon(
                          Icons.search,
                          color: AColor.textColor,
                          size: 20,
                        ),
                        suffixIcon:
                            hasText
                                ? GestureDetector(
                                  onTap: () {
                                    // Clear the search
                                    searchController.clear();
                                    debounceTimer.value?.cancel();

                                    // Reset filter to show all trainers
                                    final currentFilter =
                                        ref.read(trainerListProvider).filter;
                                    final newFilter = currentFilter.copyWith(
                                      clearSearchQuery: true,
                                    );
                                    ref
                                        .read(trainerListProvider.notifier)
                                        .applyFilter(newFilter);
                                  },
                                  child: const Icon(
                                    Icons.clear,
                                    color: AColor.textColor,
                                    size: 20,
                                  ),
                                )
                                : null,
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                      ),
                      onChanged: (value) {
                        // Cancel previous timer
                        debounceTimer.value?.cancel();

                        // Start new timer for search (even for empty values)
                        debounceTimer.value = Timer(
                          const Duration(milliseconds: 300),
                          () {
                            final currentFilter =
                                ref.read(trainerListProvider).filter;
                            final newFilter = currentFilter.copyWith(
                              searchQuery:
                                  value.trim().isEmpty ? null : value.trim(),
                            );
                            ref
                                .read(trainerListProvider.notifier)
                                .applyFilter(newFilter);
                          },
                        );
                      },
                    ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs(WidgetRef ref, TrainerListFilter currentFilter) {
    final filters = [
      'All',
      'Strength Training',
      'Cardio',
      'Yoga',
      'Pilates',
      'HIIT',
    ];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SizedBox(
        height: 36,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: filters.length,
          separatorBuilder: (context, index) => const SizedBox(width: 12),
          itemBuilder: (context, index) {
            final filter = filters[index];
            final isSelected =
                (filter == 'All' && currentFilter.specialization == null) ||
                currentFilter.specialization == filter;
            return FilterTab(
              text: filter,
              isSelected: isSelected,
              onTap: () {
                final newFilter =
                    filter == 'All'
                        ? currentFilter.copyWith(clearSpecialization: true)
                        : currentFilter.copyWith(specialization: filter);
                ref.read(trainerListProvider.notifier).applyFilter(newFilter);
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildTrainerList(WidgetRef ref, TrainerListState state) {
    if (state.isLoading && state.trainers.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFFACC15)),
      );
    }

    if (state.error != null && state.trainers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            TextWidget(
              'Error loading instructors'.hardcoded,
              style: ATextStyle.large.copyWith(color: AColor.textColor),
            ),
            const SizedBox(height: 8),
            TextWidget(
              state.error!,
              style: ATextStyle.medium.copyWith(color: AColor.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed:
                  () => ref
                      .read(trainerListProvider.notifier)
                      .loadTrainers(refresh: true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: AColor.black,
              ),
              child: TextWidget(
                'Retry'.hardcoded,
                style: ATextStyle.medium.copyWith(color: AColor.black),
              ),
            ),
          ],
        ),
      );
    }

    if (state.trainers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.search_off, color: AColor.grey, size: 64),
            const SizedBox(height: 16),
            TextWidget(
              'No instructors found'.hardcoded,
              style: ATextStyle.large.copyWith(color: AColor.textColor),
            ),
            const SizedBox(height: 8),
            TextWidget(
              'Try adjusting your search or filters'.hardcoded,
              style: ATextStyle.medium.copyWith(color: AColor.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh:
          () => ref
              .read(trainerListProvider.notifier)
              .loadTrainers(refresh: true),
      color: const Color(0xFFFACC15),
      backgroundColor: const Color(0xFF1F2937),
      child: NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification scrollInfo) {
          if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
              state.hasMore &&
              !state.isLoadingMore) {
            ref.read(trainerListProvider.notifier).loadMoreTrainers();
          }
          return false;
        },
        child: ListView.separated(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          itemCount: state.trainers.length + (state.hasMore ? 1 : 0),
          separatorBuilder: (context, index) => const SizedBox(height: 12),
          itemBuilder: (context, index) {
            if (index == state.trainers.length) {
              // Loading indicator for pagination
              return Container(
                padding: const EdgeInsets.all(16),
                alignment: Alignment.center,
                child:
                    state.isLoadingMore
                        ? const CircularProgressIndicator(
                          color: Color(0xFFFACC15),
                        )
                        : const SizedBox.shrink(),
              );
            }

            return TrainerCard(trainer: state.trainers[index]);
          },
        ),
      ),
    );
  }
}

// Filter Tab Widget
class FilterTab extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;

  const FilterTab({
    super.key,
    required this.text,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AColor.fitgoGreen : const Color(0xFF1F2937),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(
          child: TextWidget(
            text,
            style: ATextStyle.small.copyWith(
              color: isSelected ? AColor.black : AColor.textColor,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}

// Trainer Card Widget
class TrainerCard extends StatelessWidget {
  final TrainerListItem trainer;

  const TrainerCard({super.key, required this.trainer});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // Navigate to instructor detail page with correct instructorId
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TrainerDetailView(instructorId: trainer.id),
          ),
        );
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFF1F2937),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // Profile image
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                color: AColor.grey.withValues(alpha: 0.3),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child:
                    trainer.imageUrl.isNotEmpty
                        ? CachedNetworkImage(
                          imageUrl: trainer.imageUrl,
                          fit: BoxFit.cover,
                          placeholder:
                              (context, url) => const Icon(
                                Icons.person,
                                color: AColor.textColor,
                                size: 30,
                              ),
                          errorWidget:
                              (context, url, error) => const Icon(
                                Icons.person,
                                color: AColor.textColor,
                                size: 30,
                              ),
                        )
                        : const Icon(
                          Icons.person,
                          color: AColor.textColor,
                          size: 30,
                        ),
              ),
            ),

            const SizedBox(width: 12),

            // Trainer info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  TextWidget(
                    trainer.name,
                    style: ATextStyle.medium.copyWith(
                      color: AColor.textColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 2),

                  // Gym name
                  TextWidget(
                    trainer.gym,
                    style: ATextStyle.small.copyWith(color: AColor.grey),
                  ),

                  const SizedBox(height: 6),

                  // Rating
                  RatingRow(
                    rating: trainer.rating,
                    reviewCount: trainer.reviewCount,
                  ),

                  const SizedBox(height: 4),

                  // Specialty
                  TextWidget(
                    trainer.specialty,
                    style: ATextStyle.small.copyWith(color: AColor.textColor),
                  ),
                ],
              ),
            ),

            // Price section
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                TextWidget(
                  '\$${trainer.price}',
                  style: ATextStyle.medium.copyWith(
                    color: AColor.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextWidget(
                  'per session'.hardcoded,
                  style: ATextStyle.small.copyWith(
                    color: AColor.grey,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Rating Row Widget
class RatingRow extends StatelessWidget {
  final double rating;
  final int reviewCount;

  const RatingRow({super.key, required this.rating, required this.reviewCount});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Icon(
          Icons.star,
          color: Color(0xFFFACC15), // Yellow color for rating stars
          size: 14,
        ),
        const SizedBox(width: 4),
        TextWidget(
          rating.toString(),
          style: ATextStyle.small.copyWith(
            color: AColor.textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 4),
        TextWidget(
          '($reviewCount reviews)',
          style: ATextStyle.small.copyWith(color: AColor.grey, fontSize: 10),
        ),
      ],
    );
  }
}

// Filter Bottom Sheet Widget
class FilterBottomSheet extends StatelessWidget {
  final WidgetRef ref;
  const FilterBottomSheet({super.key, required this.ref});

  @override
  Widget build(BuildContext context) {
    final currentFilter = ref.watch(trainerListProvider).filter;

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header container with background color
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            ),
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextWidget(
                  'Sorting Options'.hardcoded,
                  style: ATextStyle.title.copyWith(
                    color: AColor.textColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: AColor.textColor),
                ),
              ],
            ),
          ),

          // Content area
          Flexible(
            child: Container(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ...TrainerListFilter.sortOptions.map((option) {
                      final isSelected =
                          currentFilter.sortBy == option['sortBy'] &&
                          currentFilter.ascending == option['ascending'];

                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 4,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          tileColor:
                              isSelected
                                  ? AColor.fitgoGreen.withOpacity(0.1)
                                  : const Color(0xFF374151),
                          leading: Icon(
                            _getSortIcon(option['key']),
                            color:
                                isSelected
                                    ? AColor.fitgoGreen
                                    : AColor.textColor,
                          ),
                          title: TextWidget(
                            option['label'],
                            style: ATextStyle.medium.copyWith(
                              color:
                                  isSelected
                                      ? AColor.fitgoGreen
                                      : AColor.textColor,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                          onTap: () {
                            final newFilter = currentFilter.copyWith(
                              sortBy: option['sortBy'],
                              ascending: option['ascending'],
                            );
                            ref
                                .read(trainerListProvider.notifier)
                                .applyFilter(newFilter);
                            Navigator.of(context).pop();
                          },
                        ),
                      );
                    }),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getSortIcon(String key) {
    switch (key) {
      case 'rating_desc':
      case 'rating_asc':
        return Icons.star;
      case 'price_desc':
      case 'price_asc':
        return Icons.attach_money;
      case 'capacity_desc':
      case 'capacity_asc':
        return Icons.people;
      default:
        return Icons.sort;
    }
  }
}
