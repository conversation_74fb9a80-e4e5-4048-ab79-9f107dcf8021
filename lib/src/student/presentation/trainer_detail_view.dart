import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/student/domain/trainer_detail.dart';
import 'package:fitgo_app/src/student/application/trainer_detail_provider.dart';
import 'package:fitgo_app/src/student/presentation/components/trainer_image.dart';
import 'package:fitgo_app/src/student/presentation/components/stat_card.dart';
import 'package:fitgo_app/src/student/presentation/components/tag_list.dart';
import 'package:fitgo_app/src/student/presentation/components/work_history_card.dart';
import 'package:fitgo_app/src/student/presentation/components/certification_card.dart';
import 'package:fitgo_app/src/student/presentation/components/review_card.dart';
import 'package:fitgo_app/src/student/presentation/components/reviews_modal.dart';
import 'package:fitgo_app/src/program_enrollment/presentation/program_enrollment_view.dart';

class TrainerDetailView extends HookConsumerWidget {
  final String instructorId;

  const TrainerDetailView({super.key, required this.instructorId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final trainerDetailAsync = ref.watch(
      instructorDetailProvider(instructorId),
    );

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A), // Dark background
      body: SafeArea(
        child: Column(
          children: [
            // Top app bar
            _buildTopAppBar(context),

            // Main content
            Expanded(
              child: trainerDetailAsync.when(
                data: (trainer) => _buildTrainerContent(context, ref, trainer),
                loading: () => _buildLoadingState(),
                error:
                    (error, stack) =>
                        _buildErrorState(context, error.toString()),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: trainerDetailAsync.when(
        loading: () => null,
        error: (error, stack) => null,
        data: (trainer) => _buildFloatingBookButton(context, trainer),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildTopAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Back button
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF1E293B),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Fitgo logo
          Expanded(
            child: Row(
              children: [
                TextWidget(
                  'Fit',
                  style: ATextStyle.title.copyWith(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextWidget(
                  'go',
                  style: ATextStyle.title.copyWith(
                    color: AColor.fitgoGreen,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Hamburger menu
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.menu, color: Colors.white, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildTrainerContent(
    BuildContext context,
    WidgetRef ref,
    TrainerDetail trainer,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),

          // Trainer profile image
          TrainerImage(imageUrl: trainer.photoUrl),

          const SizedBox(height: 20),

          // Trainer name and rating
          _buildNameAndRating(trainer),

          const SizedBox(height: 20),

          // Stats cards
          _buildStatsCards(trainer),

          const SizedBox(height: 24),

          // Specializations
          _buildSpecializations(trainer.specializations),

          const SizedBox(height: 24),

          // Work History
          _buildWorkHistory(trainer.workHistory),

          const SizedBox(height: 24),

          // Certifications
          _buildCertifications(trainer.certifications),

          const SizedBox(height: 24),

          // Client Reviews
          _buildClientReviews(trainer.reviews, context, ref),

          const SizedBox(height: 32),

          // Extra bottom padding for floating action button
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  Widget _buildNameAndRating(TrainerDetail trainer) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          trainer.name,
          style: ATextStyle.title.copyWith(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            const Icon(Icons.star, color: Color(0xFFFACC15), size: 20),
            const SizedBox(width: 4),
            TextWidget(
              trainer.rating.toString(),
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatsCards(TrainerDetail trainer) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'Experience'.hardcoded,
                value: '${trainer.experienceYears}+ Years',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: StatCard(
                title: 'Clients'.hardcoded,
                value: '${trainer.clientCount}+',
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Capacity card
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1E293B),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                Icons.people,
                color:
                    trainer.currentStudents < trainer.maxStudents
                        ? AColor.fitgoGreen
                        : Colors.red,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      'Student Capacity'.hardcoded,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    TextWidget(
                      '${trainer.currentStudents}/${trainer.maxStudents} students enrolled',
                      style: ATextStyle.small.copyWith(color: Colors.grey[400]),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color:
                      trainer.currentStudents < trainer.maxStudents
                          ? AColor.fitgoGreen.withValues(alpha: 0.2)
                          : Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextWidget(
                  trainer.currentStudents < trainer.maxStudents
                      ? '${trainer.maxStudents - trainer.currentStudents} spots left'
                      : 'Full',
                  style: ATextStyle.small.copyWith(
                    color:
                        trainer.currentStudents < trainer.maxStudents
                            ? AColor.fitgoGreen
                            : Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSpecializations(List<String> specializations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Specializations'.hardcoded,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        TagList(tags: specializations),
      ],
    );
  }

  Widget _buildWorkHistory(List<WorkHistory> workHistory) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Work History'.hardcoded,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (workHistory.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.work_outline, color: Colors.grey, size: 24),
                const SizedBox(width: 12),
                TextWidget(
                  'No work history available yet.'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          )
        else
          ...workHistory.map(
            (work) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: WorkHistoryCard(workHistory: work),
            ),
          ),
      ],
    );
  }

  Widget _buildCertifications(List<Certification> certifications) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Certifications'.hardcoded,
          style: ATextStyle.large.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (certifications.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.verified_outlined,
                  color: Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: 12),
                TextWidget(
                  'No certifications listed yet.'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          )
        else
          ...certifications.map(
            (cert) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: CertificationCard(certification: cert),
            ),
          ),
      ],
    );
  }

  Widget _buildClientReviews(
    List<Review> reviews,
    BuildContext context,
    WidgetRef ref,
  ) {
    // Show only first 2 reviews
    final reviewsToShow = reviews.take(2).toList();
    final hasMoreReviews = reviews.length > 2;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with Show All button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            TextWidget(
              'Client Reviews'.hardcoded,
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (hasMoreReviews)
              GestureDetector(
                onTap: () {
                  final instructorDetailAsync = ref.read(
                    instructorDetailProvider(instructorId),
                  );
                  instructorDetailAsync.whenData((instructor) {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      isDismissible: true,
                      enableDrag: true,
                      backgroundColor: Colors.transparent,
                      builder:
                          (context) => ReviewsModal(
                            instructorId: instructorId,
                            instructorName: instructor.name,
                          ),
                    );
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AColor.fitgoGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AColor.fitgoGreen.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextWidget(
                        'Show All'.hardcoded,
                        style: ATextStyle.small.copyWith(
                          color: AColor.fitgoGreen,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: AColor.fitgoGreen,
                        size: 12,
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (reviews.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1E293B),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.rate_review_outlined,
                  color: Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: 12),
                TextWidget(
                  'No client reviews yet.'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: Colors.grey),
                ),
              ],
            ),
          )
        else
          // Show first 2 reviews
          Column(
            children:
                reviewsToShow
                    .map(
                      (review) => Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: ReviewCard(review: review),
                      ),
                    )
                    .toList(),
          ),
      ],
    );
  }

  Widget _buildFloatingBookButton(BuildContext context, TrainerDetail trainer) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: FloatingActionButton.extended(
          onPressed: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder:
                    (context) => ProgramEnrollmentView(
                      trainerId: trainer.id,
                      trainerName: trainer.name,
                    ),
              ),
            );
          },
          backgroundColor: const Color(0xFF1E293B), // Dark primary color
          foregroundColor: AColor.fitgoGreen,
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
            side: BorderSide(
              color: AColor.fitgoGreen.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          icon: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AColor.fitgoGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.calendar_today,
              color: AColor.fitgoGreen,
              size: 20,
            ),
          ),
          label: TextWidget(
            'Eğitmene Kayıt Ol'.hardcoded,
            style: ATextStyle.medium.copyWith(
              color: AColor.fitgoGreen,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(color: Color(0xFFFACC15)),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    final isNotFound =
        error.toLowerCase().contains('not found') ||
        error.toLowerCase().contains('no rows returned');

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isNotFound ? Icons.person_off : Icons.error_outline,
            color: isNotFound ? Colors.orange : Colors.red,
            size: 64,
          ),
          const SizedBox(height: 16),
          TextWidget(
            isNotFound
                ? 'Trainer not found'.hardcoded
                : 'Error loading trainer details'.hardcoded,
            style: ATextStyle.large.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          TextWidget(
            isNotFound
                ? 'This trainer may no longer be available.'.hardcoded
                : error,
            style: ATextStyle.medium.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1E293B),
                  foregroundColor: Colors.white,
                ),
                child: TextWidget(
                  'Go Back'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: Colors.white),
                ),
              ),
              if (!isNotFound) ...[
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // Retry loading
                    // This will trigger a rebuild and retry the provider
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AColor.fitgoGreen,
                    foregroundColor: Colors.black,
                  ),
                  child: TextWidget(
                    'Retry'.hardcoded,
                    style: ATextStyle.medium.copyWith(color: Colors.black),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
