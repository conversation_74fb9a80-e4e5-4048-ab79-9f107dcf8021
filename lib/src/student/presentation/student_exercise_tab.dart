import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../application/student_workout_provider.dart';
import '../domain/student_workout_plan.dart';
import '../../shared/utils/app_logger.dart';

/// Exercise Tab for Students - Shows assigned workout plans
class StudentExerciseTab extends HookConsumerWidget {
  const StudentExerciseTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPlanIndex = useState(0);

    // Get current user ID (student)
    final currentUser = Supabase.instance.client.auth.currentUser;
    if (currentUser == null) {
      AppLogger.warning('User not logged in - cannot load workout plan', tag: 'EXERCISE_TAB');
      return const Center(
        child: Text(
          'Please log in to view your workout plan',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    AppLogger.info('Loading exercise tab for student: ${currentUser.id}', tag: 'EXERCISE_TAB');
    final workoutPlanAsync = ref.watch(studentWorkoutPlanProvider(currentUser.id));

    return workoutPlanAsync.when(
      data: (workoutPlan) {
        if (workoutPlan == null) {
          AppLogger.info('No workout plan assigned to student', tag: 'EXERCISE_TAB');
          return _buildEmptyState();
        }
        AppLogger.success('Workout plan loaded successfully: ${workoutPlan.planName}', tag: 'EXERCISE_TAB');
        AppLogger.info('Available plans: ${workoutPlan.plans.map((p) => p.name).join(', ')}', tag: 'EXERCISE_TAB');
        return _buildWorkoutPlanView(context, ref, workoutPlan, selectedPlanIndex);
      },
      loading: () {
        AppLogger.info('Loading workout plan...', tag: 'EXERCISE_TAB');
        return const Center(
          child: CircularProgressIndicator(color: Color(0xFFFACC15)),
        );
      },
      error: (error, stack) {
        AppLogger.error('Failed to load workout plan', tag: 'EXERCISE_TAB', error: error, stackTrace: stack);
        return _buildErrorState(error.toString());
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.fitness_center,
              size: 64,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 16),
            const Text(
              'No exercises assigned yet',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your instructor will assign workout plans for you soon!',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFFACC15).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFFFACC15).withOpacity(0.3),
                ),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: Color(0xFFFACC15),
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'Stay motivated and check back later!',
                    style: TextStyle(
                      color: Color(0xFFFACC15),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'Error loading workout plan',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutPlanView(
    BuildContext context,
    WidgetRef ref,
    StudentWorkoutPlan workoutPlan,
    ValueNotifier<int> selectedPlanIndex,
  ) {
    return Column(
      children: [
        // Header with FitGo logo and tabs
        _buildHeader(workoutPlan),
        
        // Plan selector tabs
        if (workoutPlan.plans.length > 1)
          _buildPlanTabs(workoutPlan, selectedPlanIndex),
        
        // Workout content
        Expanded(
          child: _buildWorkoutContent(context, ref, workoutPlan, selectedPlanIndex.value),
        ),
      ],
    );
  }

  Widget _buildHeader(StudentWorkoutPlan workoutPlan) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF111827),
        border: Border(
          bottom: BorderSide(color: Color(0xFF374151), width: 1),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // FitGo Logo
            const Text(
              'FitGo',
              style: TextStyle(
                color: Color(0xFFFACC15),
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Exercise/Nutrition tabs
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFF1F2937),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFACC15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'Exercise',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(0xFF111827),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: const Text(
                        'Nutrition',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(0xFF9CA3AF),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanTabs(StudentWorkoutPlan workoutPlan, ValueNotifier<int> selectedPlanIndex) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: workoutPlan.plans.length,
        itemBuilder: (context, index) {
          final isSelected = selectedPlanIndex.value == index;
          return GestureDetector(
            onTap: () {
              AppLogger.userAction('Selected workout plan: ${workoutPlan.plans[index].name}', tag: 'EXERCISE_TAB');
              selectedPlanIndex.value = index;
            },
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF1F2937),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected ? const Color(0xFFFACC15) : const Color(0xFF374151),
                ),
              ),
              child: Center(
                child: Text(
                  workoutPlan.plans[index].name,
                  style: TextStyle(
                    color: isSelected ? const Color(0xFF111827) : const Color(0xFF9CA3AF),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildWorkoutContent(
    BuildContext context,
    WidgetRef ref,
    StudentWorkoutPlan workoutPlan,
    int selectedPlanIndex,
  ) {
    if (selectedPlanIndex >= workoutPlan.plans.length) {
      return const Center(
        child: Text(
          'Plan not found',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    final selectedPlan = workoutPlan.plans[selectedPlanIndex];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workout Notes
          if (workoutPlan.notes != null && workoutPlan.notes!.isNotEmpty)
            _buildWorkoutNotes(workoutPlan.notes!),

          // Plan Info
          _buildPlanInfo(selectedPlan),

          const SizedBox(height: 16),

          // Exercise List
          if (selectedPlan.exercises.isNotEmpty)
            _buildExerciseList(context, ref, selectedPlan.exercises, workoutPlan)
          else
            _buildEmptyPlanState(),

          // Footer
          _buildFooter(workoutPlan),
        ],
      ),
    );
  }

  Widget _buildWorkoutNotes(String notes) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.note_alt_outlined,
                color: Color(0xFFFACC15),
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Workout Notes',
                style: TextStyle(
                  color: Color(0xFFFACC15),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            notes,
            style: const TextStyle(
              color: Color(0xFFE5E7EB),
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanInfo(WorkoutPlanDay plan) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            plan.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.timer_outlined,
                color: Colors.grey[400],
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '${plan.estimatedDuration} minutes',
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.fitness_center,
                color: Colors.grey[400],
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '${plan.exerciseCount} exercises',
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
              ),
            ],
          ),
          if (plan.description != null && plan.description!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              plan.description!,
              style: TextStyle(
                color: Colors.grey[300],
                fontSize: 14,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildExerciseList(
    BuildContext context,
    WidgetRef ref,
    List<WorkoutExercise> exercises,
    StudentWorkoutPlan workoutPlan,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Exercise List',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...exercises.map((exercise) => _buildExerciseCard(context, ref, exercise, workoutPlan)),
      ],
    );
  }

  Widget _buildExerciseCard(
    BuildContext context,
    WidgetRef ref,
    WorkoutExercise exercise,
    StudentWorkoutPlan workoutPlan,
  ) {
    final repository = ref.read(studentWorkoutRepositoryProvider);
    final isCompleted = repository.isExerciseCompleted(
      planData: workoutPlan.planData,
      exerciseId: exercise.id,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCompleted ? const Color(0xFFFACC15) : const Color(0xFF374151),
        ),
      ),
      child: Column(
        children: [
          // Exercise Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Exercise Image
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: const Color(0xFF374151),
                    borderRadius: BorderRadius.circular(8),
                    image: exercise.imageUrl != null
                        ? DecorationImage(
                            image: NetworkImage(exercise.imageUrl!),
                            fit: BoxFit.cover,
                          )
                        : null,
                  ),
                  child: exercise.imageUrl == null
                      ? const Icon(
                          Icons.fitness_center,
                          color: Color(0xFFFACC15),
                          size: 24,
                        )
                      : null,
                ),
                const SizedBox(width: 12),

                // Exercise Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        exercise.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        exercise.primaryMuscleGroup,
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          if (exercise.setsRepsDisplay.isNotEmpty) ...[
                            Text(
                              exercise.setsRepsDisplay,
                              style: const TextStyle(
                                color: Color(0xFFFACC15),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (exercise.weightDisplay.isNotEmpty) ...[
                              const SizedBox(width: 8),
                              Text(
                                exercise.weightDisplay,
                                style: TextStyle(
                                  color: Colors.grey[400],
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ],
                        ],
                      ),
                    ],
                  ),
                ),

                // Completion Status
                Icon(
                  isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                  color: isCompleted ? const Color(0xFFFACC15) : Colors.grey[600],
                  size: 24,
                ),
              ],
            ),
          ),

          // Rest Time & Notes
          if (exercise.restTimeDisplay.isNotEmpty || (exercise.notes != null && exercise.notes!.isNotEmpty))
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                color: Color(0xFF111827),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (exercise.restTimeDisplay.isNotEmpty)
                    Row(
                      children: [
                        Icon(
                          Icons.timer,
                          color: Colors.grey[500],
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          exercise.restTimeDisplay,
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  if (exercise.notes != null && exercise.notes!.isNotEmpty) ...[
                    if (exercise.restTimeDisplay.isNotEmpty) const SizedBox(height: 8),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.note_outlined,
                          color: Colors.grey[500],
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            exercise.notes!,
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyPlanState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.fitness_center_outlined,
              size: 48,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 16),
            Text(
              'No exercises in this plan',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your instructor will add exercises soon!',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter(StudentWorkoutPlan workoutPlan) {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.person,
                color: Colors.grey[400],
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                'Instructor: ${workoutPlan.instructorName}',
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
              ),
            ],
          ),
          if (workoutPlan.daysSinceLastTraining != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: Colors.grey[400],
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  'Last training: ${workoutPlan.daysSinceLastTraining} days ago',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
