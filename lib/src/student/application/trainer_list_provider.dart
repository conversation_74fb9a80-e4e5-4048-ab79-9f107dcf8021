import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/student/domain/trainer_list.dart';

// Constants for pagination
const int kTrainersPerPage = 10;

// State class for trainer list with pagination
class TrainerListState {
  final List<TrainerListItem> trainers;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final bool hasMore;
  final int currentPage;
  final TrainerListFilter filter;

  const TrainerListState({
    this.trainers = const [],
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.hasMore = true,
    this.currentPage = 0,
    this.filter = const TrainerListFilter(),
  });

  TrainerListState copyWith({
    List<TrainerListItem>? trainers,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    bool? hasMore,
    int? currentPage,
    TrainerListFilter? filter,
  }) {
    return TrainerListState(
      trainers: trainers ?? this.trainers,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error ?? this.error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      filter: filter ?? this.filter,
    );
  }
}

// Provider for trainer list state
final trainerListProvider = StateNotifierProvider<TrainerListNotifier, TrainerListState>((ref) {
  return TrainerListNotifier(ref.read(trainerListRepositoryProvider));
});

// Repository provider
final trainerListRepositoryProvider = Provider<TrainerListRepository>((ref) {
  return TrainerListRepository();
});

class TrainerListNotifier extends StateNotifier<TrainerListState> {
  final TrainerListRepository _repository;

  TrainerListNotifier(this._repository) : super(const TrainerListState()) {
    loadTrainers();
  }

  Future<void> loadTrainers({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(
        trainers: [],
        currentPage: 0,
        hasMore: true,
        error: null,
      );
    }

    if (state.isLoading || state.isLoadingMore) return;

    state = state.copyWith(
      isLoading: refresh || state.trainers.isEmpty,
      error: null,
    );

    try {
      final response = await _repository.getInstructors(
        page: 0,
        filter: state.filter,
      );

      state = state.copyWith(
        trainers: response.trainers,
        hasMore: response.hasMore,
        currentPage: 0,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  Future<void> loadMoreTrainers() async {
    if (!state.hasMore || state.isLoading || state.isLoadingMore) return;

    state = state.copyWith(isLoadingMore: true, error: null);

    try {
      final nextPage = state.currentPage + 1;
      final response = await _repository.getInstructors(
        page: nextPage,
        filter: state.filter,
      );

      state = state.copyWith(
        trainers: [...state.trainers, ...response.trainers],
        hasMore: response.hasMore,
        currentPage: nextPage,
        isLoadingMore: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoadingMore: false,
      );
    }
  }

  Future<void> applyFilter(TrainerListFilter filter) async {
    state = state.copyWith(filter: filter);
    await loadTrainers(refresh: true);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

class TrainerListRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  Future<TrainerListResponse> getInstructors({
    int page = 0,
    TrainerListFilter filter = const TrainerListFilter(),
  }) async {
    try {
      final startIndex = page * kTrainersPerPage;
      final endIndex = startIndex + kTrainersPerPage - 1;

      // Build query with filters
      var query = _supabase
          .from('instructors')
          .select('''
            id,
            name,
            photo_url,
            rating,
            experience_years,
            client_count,
            specializations,
            price_per_session,
            gym_affiliation,
            review_count
          ''');

      // Apply filters
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        query = query.ilike('name', '%${filter.searchQuery}%');
      }

      if (filter.specialization != null && filter.specialization != 'All') {
        query = query.contains('specializations', [filter.specialization]);
      }

      if (filter.minRating != null) {
        query = query.gte('rating', filter.minRating!);
      }

      if (filter.maxPrice != null) {
        query = query.lte('price_per_session', filter.maxPrice!);
      }

      // Apply sorting
      final sortedQuery = query.order(filter.sortBy, ascending: filter.ascending);

      // Apply pagination
      final response = await sortedQuery.range(startIndex, endIndex);

      // Get total count for pagination
      final countResponse = await _supabase
          .from('instructors')
          .select('id')
          .count(CountOption.exact);

      final totalCount = countResponse.count;
      final trainers = (response as List<dynamic>)
          .map((item) => TrainerListItem.fromJson(item as Map<String, dynamic>))
          .toList();

      final hasMore = (startIndex + trainers.length) < totalCount;

      return TrainerListResponse(
        trainers: trainers,
        totalCount: totalCount,
        hasMore: hasMore,
        currentPage: page,
      );
    } on PostgrestException catch (e) {
      throw Exception('Failed to fetch trainers: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  // Method to create sample instructors for testing
  Future<void> createSampleInstructors() async {
    try {
      final sampleInstructors = [
        {
          'name': 'Michael Anderson',
          'email': '<EMAIL>',
          'photo_url': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
          'rating': 4.8,
          'experience_years': 8,
          'client_count': 200,
          'specializations': ['Weight Loss', 'Strength Training', 'HIIT', 'Nutrition'],
          'price_per_session': 85,
          'gym_affiliation': 'MacFit Istanbul',
          'review_count': 156,
        },
        {
          'name': 'Sarah Johnson',
          'email': '<EMAIL>',
          'photo_url': 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400',
          'rating': 4.9,
          'experience_years': 6,
          'client_count': 124,
          'specializations': ['Strength Training', 'HIIT', 'Functional Training'],
          'price_per_session': 90,
          'gym_affiliation': 'Elite Fitness Center',
          'review_count': 124,
        },
        {
          'name': 'Mike Chen',
          'email': '<EMAIL>',
          'photo_url': 'https://images.unsplash.com/photo-1566753323558-f4e0952af115?w=400',
          'rating': 4.7,
          'experience_years': 5,
          'client_count': 89,
          'specializations': ['Cardio', 'Weight Loss', 'Endurance'],
          'price_per_session': 75,
          'gym_affiliation': 'PowerHouse Gym',
          'review_count': 89,
        },
        {
          'name': 'Emma Wilson',
          'email': '<EMAIL>',
          'photo_url': 'https://images.unsplash.com/photo-1518611012118-696072aa579a?w=400',
          'rating': 4.9,
          'experience_years': 7,
          'client_count': 156,
          'specializations': ['Yoga', 'Pilates', 'Flexibility', 'Mindfulness'],
          'price_per_session': 65,
          'gym_affiliation': 'Zen Yoga Studio',
          'review_count': 156,
        },
        {
          'name': 'David Rodriguez',
          'email': '<EMAIL>',
          'photo_url': 'https://images.unsplash.com/photo-1583468982228-19f19164aee2?w=400',
          'rating': 4.6,
          'experience_years': 9,
          'client_count': 203,
          'specializations': ['Strength Training', 'Powerlifting', 'Bodybuilding'],
          'price_per_session': 95,
          'gym_affiliation': 'Iron Temple',
          'review_count': 203,
        },
      ];

      for (final instructor in sampleInstructors) {
        await _supabase.from('instructors').upsert(instructor);
      }
    } catch (e) {
      print('Error creating sample instructors: $e');
    }
  }
}
