import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/student/domain/trainer_detail.dart';

// Provider for fetching instructor details
final instructorDetailProvider = FutureProvider.family<TrainerDetail, String>((
  ref,
  instructorId,
) async {
  return ref
      .read(trainerDetailRepositoryProvider)
      .getInstructorDetail(instructorId);
});

// Repository provider
final trainerDetailRepositoryProvider = Provider<TrainerDetailRepository>((
  ref,
) {
  return TrainerDetailRepository();
});

class TrainerDetailRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  Future<TrainerDetail> getInstructorDetail(String instructorId) async {
    try {
      // Fetch instructor basic info
      final instructorResponse =
          await _supabase
              .from('instructors')
              .select('''
            id,
            name,
            photo_url,
            gym_affiliation,
            rating,
            experience_years,
            client_count,
            max_students,
            current_students,
            specializations
          ''')
              .eq('id', instructorId)
              .maybeSingle();

      if (instructorResponse == null) {
        throw Exception('Instructor not found with ID: $instructorId');
      }

      // Fetch work history
      final workHistoryResponse = await _supabase
          .from('trainer_work_history')
          .select('gym, role, duration')
          .eq('trainer_id', instructorId)
          .order('created_at', ascending: false);

      // Fetch certifications
      final certificationsResponse = await _supabase
          .from('trainer_certifications')
          .select('title, organization')
          .eq('trainer_id', instructorId)
          .order('created_at', ascending: false);

      // Fetch reviews
      final reviewsResponse = await _supabase
          .from('trainer_reviews')
          .select('''
            id,
            client_name,
            avatar_url,
            rating,
            comment,
            created_at
          ''')
          .eq('trainer_id', instructorId)
          .order('created_at', ascending: false)
          .limit(10); // Limit to latest 10 reviews

      // Parse work history
      final workHistory =
          (workHistoryResponse as List<dynamic>)
              .map((item) => WorkHistory.fromJson(item as Map<String, dynamic>))
              .toList();

      // Parse certifications
      final certifications =
          (certificationsResponse as List<dynamic>)
              .map(
                (item) => Certification.fromJson(item as Map<String, dynamic>),
              )
              .toList();

      // Parse reviews
      final reviews =
          (reviewsResponse as List<dynamic>)
              .map((item) => Review.fromJson(item as Map<String, dynamic>))
              .toList();

      // Combine all data
      final instructorData = Map<String, dynamic>.from(instructorResponse);
      instructorData['work_history'] =
          workHistory.map((e) => e.toJson()).toList();
      instructorData['certifications'] =
          certifications.map((e) => e.toJson()).toList();
      instructorData['reviews'] = reviews.map((e) => e.toJson()).toList();

      final instructorDetail = TrainerDetail.fromJson(instructorData);

      return instructorDetail;
    } on PostgrestException catch (e) {
      throw Exception('Failed to fetch instructor details: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  // Method to create sample data for testing
  Future<void> createSampleInstructorData() async {
    try {
      // Insert sample instructor
      final instructorId = 'instructor_001';

      await _supabase.from('instructors').upsert({
        'name': 'Michael Anderson',
        'email': '<EMAIL>',
        'photo_url':
            'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
        'rating': 4.8,
        'experience_years': 8,
        'client_count': 200,
        'specializations': [
          'Weight Loss',
          'Strength Training',
          'HIIT',
          'Nutrition',
        ],
      });

      // Insert work history
      await _supabase.from('trainer_work_history').upsert([
        {
          'trainer_id': instructorId,
          'gym': 'MacFit - Istanbul',
          'role': 'Gym Owner',
          'duration': '3 years',
        },
        {
          'trainer_id': instructorId,
          'gym': 'Fit Gym - Istanbul',
          'role': 'Instructor',
          'duration': '1 year',
        },
        {
          'trainer_id': instructorId,
          'gym': 'Fibo Fit - Izmir',
          'role': 'Instructor',
          'duration': '2 years',
        },
      ]);

      // Insert certifications
      await _supabase.from('trainer_certifications').upsert([
        {
          'trainer_id': instructorId,
          'title': 'NASM Certified Personal Trainer',
          'organization': 'National Academy of Sports Medicine',
        },
        {
          'trainer_id': instructorId,
          'title': 'Precision Nutrition Level 1',
          'organization': 'Precision Nutrition',
        },
      ]);

      // Insert reviews
      await _supabase.from('instructor_reviews').upsert([
        {
          'instructor_id': instructorId,
          'client_name': 'Sarah Thompson',
          'avatar_url':
              'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
          'rating': 5.0,
          'comment':
              'Michael is an inspirational instructor! His expertise in strength training helped me achieve my fitness goals. I highly recommend him!',
        },
        {
          'instructor_id': instructorId,
          'client_name': 'James Wilson',
          'avatar_url':
              'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
          'rating': 4.5,
          'comment':
              'Great attention to form and technique. Michael\'s significant guidance has been instrumental in my transformation journey!',
        },
      ]);
    } catch (e) {
      print('Error creating sample data: $e');
    }
  }
}
