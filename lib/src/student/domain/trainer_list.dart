import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';

class TrainerListItem {
  final String id;
  final String name;
  final String gym;
  final double rating;
  final int reviewCount;
  final String specialty;
  final int price;
  final String imageUrl;
  final List<String> specializations;
  final int experienceYears;
  final int clientCount;
  final int maxCapacity;
  final int currentEnrollment;
  final int availableSpots;

  TrainerListItem({
    required this.id,
    required this.name,
    required this.gym,
    required this.rating,
    required this.reviewCount,
    required this.specialty,
    required this.price,
    required this.imageUrl,
    required this.specializations,
    required this.experienceYears,
    required this.clientCount,
    required this.maxCapacity,
    required this.currentEnrollment,
    required this.availableSpots,
  });

  factory TrainerListItem.fromJson(Map<String, dynamic> json) {
    return TrainerListItem(
      id: json['id'] as String,
      name: json['name'] as String,
      gym: json['gym'] as String? ?? 'Fitness Center',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: json['review_count'] as int? ?? 0,
      specialty: json['specialty'] as String? ?? 'General Fitness',
      price: json['price_per_session'] as int? ?? 50,
      imageUrl: json['photo_url'] as String? ?? '',
      specializations:
          (json['specializations'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      experienceYears: json['experience_years'] as int? ?? 0,
      clientCount: json['client_count'] as int? ?? 0,
      maxCapacity: json['max_course_capacity'] as int? ?? 50,
      currentEnrollment: json['total_course_enrollments'] as int? ?? 0,
      availableSpots: json['available_spots'] as int? ?? 50,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'gym': gym,
      'rating': rating,
      'review_count': reviewCount,
      'specialty': specialty,
      'price_per_session': price,
      'photo_url': imageUrl,
      'specializations': specializations,
      'experience_years': experienceYears,
      'client_count': clientCount,
      'max_course_capacity': maxCapacity,
      'total_course_enrollments': currentEnrollment,
      'available_spots': availableSpots,
    };
  }
}

class TrainerListResponse {
  final List<TrainerListItem> trainers;
  final int totalCount;
  final bool hasMore;
  final int currentPage;

  TrainerListResponse({
    required this.trainers,
    required this.totalCount,
    required this.hasMore,
    required this.currentPage,
  });

  factory TrainerListResponse.fromJson(Map<String, dynamic> json) {
    return TrainerListResponse(
      trainers:
          (json['trainers'] as List<dynamic>)
              .map((e) => TrainerListItem.fromJson(e as Map<String, dynamic>))
              .toList(),
      totalCount: json['total_count'] as int,
      hasMore: json['has_more'] as bool,
      currentPage: json['current_page'] as int,
    );
  }
}

class TrainerListFilter {
  final String? searchQuery;
  final String? specialization;
  final double? minRating;
  final int? maxPrice;
  final String sortBy;
  final bool ascending;

  const TrainerListFilter({
    this.searchQuery,
    this.specialization,
    this.minRating,
    this.maxPrice,
    this.sortBy = 'rating',
    this.ascending = false,
  });

  // Helper getters for sort options
  static List<Map<String, dynamic>> sortOptions = [
    {
      'key': 'rating_desc',
      'label': 'Decreasing Rating'.hardcoded,
      'sortBy': 'rating',
      'ascending': false,
    },
    {
      'key': 'rating_asc',
      'label': 'Increasing Rating'.hardcoded,
      'sortBy': 'rating',
      'ascending': true,
    },
    {
      'key': 'price_desc',
      'label': 'Decreasing Price'.hardcoded,
      'sortBy': 'price_per_session',
      'ascending': false,
    },
    {
      'key': 'price_asc',
      'label': 'Increasing Price'.hardcoded,
      'sortBy': 'price_per_session',
      'ascending': true,
    },
    {
      'key': 'capacity_desc',
      'label': 'Decreasing Capacity'.hardcoded,
      'sortBy': 'available_spots',
      'ascending': false,
    },
    {
      'key': 'capacity_asc',
      'label': 'Increasing Capacity'.hardcoded,
      'sortBy': 'available_spots',
      'ascending': true,
    },
  ];

  TrainerListFilter copyWith({
    String? searchQuery,
    String? specialization,
    double? minRating,
    int? maxPrice,
    String? sortBy,
    bool? ascending,
    bool clearSearchQuery = false,
    bool clearSpecialization = false,
    bool clearMinRating = false,
    bool clearMaxPrice = false,
  }) {
    return TrainerListFilter(
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
      specialization:
          clearSpecialization ? null : (specialization ?? this.specialization),
      minRating: clearMinRating ? null : (minRating ?? this.minRating),
      maxPrice: clearMaxPrice ? null : (maxPrice ?? this.maxPrice),
      sortBy: sortBy ?? this.sortBy,
      ascending: ascending ?? this.ascending,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'search_query': searchQuery,
      'specialization': specialization,
      'min_rating': minRating,
      'max_price': maxPrice,
      'sort_by': sortBy,
      'ascending': ascending,
    };
  }
}
