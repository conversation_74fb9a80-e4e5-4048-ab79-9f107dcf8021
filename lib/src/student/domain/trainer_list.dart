class TrainerListItem {
  final String id;
  final String name;
  final String gym;
  final double rating;
  final int reviewCount;
  final String specialty;
  final int price;
  final String imageUrl;
  final List<String> specializations;
  final int experienceYears;
  final int clientCount;

  TrainerListItem({
    required this.id,
    required this.name,
    required this.gym,
    required this.rating,
    required this.reviewCount,
    required this.specialty,
    required this.price,
    required this.imageUrl,
    required this.specializations,
    required this.experienceYears,
    required this.clientCount,
  });

  factory TrainerListItem.fromJson(Map<String, dynamic> json) {
    return TrainerListItem(
      id: json['id'] as String,
      name: json['name'] as String,
      gym: json['gym'] as String? ?? 'Fitness Center',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: json['review_count'] as int? ?? 0,
      specialty: json['specialty'] as String? ?? 'General Fitness',
      price: json['price_per_session'] as int? ?? 50,
      imageUrl: json['photo_url'] as String? ?? '',
      specializations: (json['specializations'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList() ?? [],
      experienceYears: json['experience_years'] as int? ?? 0,
      clientCount: json['client_count'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'gym': gym,
      'rating': rating,
      'review_count': reviewCount,
      'specialty': specialty,
      'price_per_session': price,
      'photo_url': imageUrl,
      'specializations': specializations,
      'experience_years': experienceYears,
      'client_count': clientCount,
    };
  }
}

class TrainerListResponse {
  final List<TrainerListItem> trainers;
  final int totalCount;
  final bool hasMore;
  final int currentPage;

  TrainerListResponse({
    required this.trainers,
    required this.totalCount,
    required this.hasMore,
    required this.currentPage,
  });

  factory TrainerListResponse.fromJson(Map<String, dynamic> json) {
    return TrainerListResponse(
      trainers: (json['trainers'] as List<dynamic>)
          .map((e) => TrainerListItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: json['total_count'] as int,
      hasMore: json['has_more'] as bool,
      currentPage: json['current_page'] as int,
    );
  }
}

class TrainerListFilter {
  final String? searchQuery;
  final String? specialization;
  final double? minRating;
  final int? maxPrice;
  final String sortBy;
  final bool ascending;

  const TrainerListFilter({
    this.searchQuery,
    this.specialization,
    this.minRating,
    this.maxPrice,
    this.sortBy = 'rating',
    this.ascending = false,
  });

  TrainerListFilter copyWith({
    String? searchQuery,
    String? specialization,
    double? minRating,
    int? maxPrice,
    String? sortBy,
    bool? ascending,
  }) {
    return TrainerListFilter(
      searchQuery: searchQuery ?? this.searchQuery,
      specialization: specialization ?? this.specialization,
      minRating: minRating ?? this.minRating,
      maxPrice: maxPrice ?? this.maxPrice,
      sortBy: sortBy ?? this.sortBy,
      ascending: ascending ?? this.ascending,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'search_query': searchQuery,
      'specialization': specialization,
      'min_rating': minRating,
      'max_price': maxPrice,
      'sort_by': sortBy,
      'ascending': ascending,
    };
  }
}
