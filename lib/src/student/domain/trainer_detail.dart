class TrainerDetail {
  final String id;
  final String name;
  final String photoUrl;
  final String gym;
  final double rating;
  final int experienceYears;
  final int clientCount;
  final int maxStudents;
  final int currentStudents;
  final List<String> specializations;
  final List<WorkHistory> workHistory;
  final List<Certification> certifications;
  final List<Review> reviews;

  TrainerDetail({
    required this.id,
    required this.name,
    required this.photoUrl,
    required this.gym,
    required this.rating,
    required this.experienceYears,
    required this.clientCount,
    required this.maxStudents,
    required this.currentStudents,
    required this.specializations,
    required this.workHistory,
    required this.certifications,
    required this.reviews,
  });

  factory TrainerDetail.fromJson(Map<String, dynamic> json) {
    return TrainerDetail(
      id: json['id'] as String,
      name: json['name'] as String,
      photoUrl: json['photo_url'] as String? ?? '',
      gym: json['gym_affiliation'] as String? ?? 'Fitness Center',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      experienceYears: json['experience_years'] as int? ?? 0,
      clientCount: json['client_count'] as int? ?? 0,
      maxStudents: json['max_students'] as int? ?? 50,
      currentStudents: json['current_students'] as int? ?? 0,
      specializations:
          (json['specializations'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      workHistory:
          (json['work_history'] as List<dynamic>?)
              ?.map((e) => WorkHistory.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      certifications:
          (json['certifications'] as List<dynamic>?)
              ?.map((e) => Certification.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      reviews:
          (json['reviews'] as List<dynamic>?)
              ?.map((e) => Review.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'photo_url': photoUrl,
      'gym_affiliation': gym,
      'rating': rating,
      'experience_years': experienceYears,
      'client_count': clientCount,
      'max_students': maxStudents,
      'current_students': currentStudents,
      'specializations': specializations,
      'work_history': workHistory.map((e) => e.toJson()).toList(),
      'certifications': certifications.map((e) => e.toJson()).toList(),
      'reviews': reviews.map((e) => e.toJson()).toList(),
    };
  }
}

class WorkHistory {
  final String gym;
  final String role;
  final String duration;

  WorkHistory({required this.gym, required this.role, required this.duration});

  factory WorkHistory.fromJson(Map<String, dynamic> json) {
    return WorkHistory(
      gym: json['gym'] as String,
      role: json['role'] as String,
      duration: json['duration'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'gym': gym, 'role': role, 'duration': duration};
  }
}

class Certification {
  final String title;
  final String organization;

  Certification({required this.title, required this.organization});

  factory Certification.fromJson(Map<String, dynamic> json) {
    return Certification(
      title: json['title'] as String,
      organization: json['organization'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'title': title, 'organization': organization};
  }
}

class Review {
  final String id;
  final String clientName;
  final String avatarUrl;
  final double rating;
  final String comment;
  final DateTime createdAt;

  Review({
    required this.id,
    required this.clientName,
    required this.avatarUrl,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'].toString(),
      clientName: json['client_name'] as String,
      avatarUrl: json['avatar_url'] as String? ?? '',
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      comment: json['comment'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'client_name': clientName,
      'avatar_url': avatarUrl,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
