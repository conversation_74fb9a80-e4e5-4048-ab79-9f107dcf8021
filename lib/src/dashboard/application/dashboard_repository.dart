import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/dashboard/domain/dashboard_models.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class DashboardRepository {
  final SupabaseClient _supabase = Supabase.instance.client;
  static const String _onboardingStatusKey = 'onboarding_status';

  // Get default dashboard tiles
  List<DashboardTile> getDefaultDashboardTiles() {
    return [
      const DashboardTile(
        id: 'reinforcement_plan',
        title: 'Reinforcement Plan',
        subtitle: 'A personalized nutrition guide.',
        icon: Icons.restaurant_menu,
        iconColor: AColor.fitgoGreen,
        route: '/reinforcement-plan',
      ),
      const DashboardTile(
        id: 'training_notes',
        title: 'Training Notes',
        subtitle: 'Save your progress',
        icon: Icons.note_alt,
        iconColor: AColor.fitgoGreen,
        route: '/training-notes',
      ),
      const DashboardTile(
        id: 'weight_record',
        title: 'Weight Record Book',
        subtitle: 'Track your removal statistics.',
        icon: Icons.monitor_weight,
        iconColor: AColor.fitgoGreen,
        route: '/weight-record',
      ),
      const DashboardTile(
        id: 'education_videos',
        title: 'Education Videos',
        subtitle: 'Learn professionalism.',
        icon: Icons.play_circle_filled,
        iconColor: AColor.fitgoGreen,
        route: '/education-videos',
      ),
      const DashboardTile(
        id: 'healthy_recipes',
        title: 'Healthy Recipes',
        subtitle: 'Nutritious meal ideas.',
        icon: Icons.local_dining,
        iconColor: AColor.fitgoGreen,
        route: '/healthy-recipes',
      ),
      const DashboardTile(
        id: 'my_questions',
        title: 'My Questions',
        subtitle: 'Get expert answers.',
        icon: Icons.help_center,
        iconColor: AColor.fitgoGreen,
        route: '/my-questions',
        hasNotification: true,
        notificationCount: 2,
      ),
      const DashboardTile(
        id: 'feedback',
        title: 'Feedback',
        subtitle: 'Track your progress with expert feedback.',
        icon: Icons.feedback,
        iconColor: AColor.fitgoGreen,
        route: '/feedback',
      ),
      const DashboardTile(
        id: 'shopping_list',
        title: 'Shopping List',
        subtitle: 'Nutritious meal ideas.',
        icon: Icons.shopping_cart,
        iconColor: AColor.fitgoGreen,
        route: '/shopping-list',
      ),
      const DashboardTile(
        id: 'market',
        title: 'Market',
        subtitle: 'Plan your shopping.',
        icon: Icons.store,
        iconColor: AColor.fitgoGreen,
        route: '/market',
      ),
      const DashboardTile(
        id: 'workout_reminders',
        title: 'Workout Reminders',
        subtitle: 'Set workout notifications.',
        icon: Icons.alarm,
        iconColor: AColor.fitgoGreen,
        route: '/workout-reminders',
      ),
    ];
  }

  // Get user profile for dashboard
  Future<DashboardUserProfile?> getUserProfile() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        // Return mock user for development
        return const DashboardUserProfile(
          userId: 'mock_user',
          name: 'Orkun Hacılar',
          email: '<EMAIL>',
          subscriptionStatus: SubscriptionStatus.premium,
        );
      }

      // Try to get profile from Supabase
      final response =
          await _supabase
              .from('user_profiles')
              .select('*')
              .eq('user_id', user.id)
              .maybeSingle();

      if (response != null) {
        return DashboardUserProfile(
          userId: user.id,
          name:
              response['full_name'] as String? ??
              user.email?.split('@')[0] ??
              'User',
          email: user.email,
          avatarUrl: response['avatar_url'] as String?,
          subscriptionStatus: _parseSubscriptionStatus(
            response['subscription_status'] as String?,
          ),
        );
      } else {
        // Fallback to user metadata
        return DashboardUserProfile(
          userId: user.id,
          name:
              user.userMetadata?['full_name'] as String? ??
              user.email?.split('@')[0] ??
              'User',
          email: user.email,
          subscriptionStatus:
              SubscriptionStatus.premium, // Default for new users
        );
      }
    } catch (e) {
      // Return mock user on error
      return const DashboardUserProfile(
        userId: 'mock_user',
        name: 'Orkun Hacılar',
        email: '<EMAIL>',
        subscriptionStatus: SubscriptionStatus.premium,
      );
    }
  }

  // Parse subscription status from string
  SubscriptionStatus _parseSubscriptionStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'premium':
        return SubscriptionStatus.premium;
      case 'expired':
        return SubscriptionStatus.expired;
      default:
        return SubscriptionStatus.free;
    }
  }

  // Get onboarding status
  Future<OnboardingStatus> getOnboardingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statusJson = prefs.getString(_onboardingStatusKey);

      if (statusJson != null) {
        // Parse from local storage
        final Map<String, dynamic> data = {};
        // Simple parsing since we're storing boolean values
        data['has_completed_payment'] =
            prefs.getBool('has_completed_payment') ?? false;
        data['has_completed_profile_form'] =
            prefs.getBool('has_completed_profile_form') ?? false;
        data['has_seen_dashboard'] =
            prefs.getBool('has_seen_dashboard') ?? false;
        data['has_assigned_plan'] = prefs.getBool('has_assigned_plan') ?? false;

        return OnboardingStatus.fromJson(data);
      }

      // Check Supabase for profile completion
      final user = _supabase.auth.currentUser;
      if (user != null) {
        final profileResponse =
            await _supabase
                .from('user_profiles')
                .select('id')
                .eq('user_id', user.id)
                .maybeSingle();

        final hasCompletedProfileForm = profileResponse != null;

        // Check if user has assigned plan
        final hasAssignedPlan = await _checkUserHasAssignedPlan(user.id);

        final status = OnboardingStatus(
          hasCompletedPayment: true, // Assume payment completed if user exists
          hasCompletedProfileForm: hasCompletedProfileForm,
          hasSeenDashboard: false,
          hasAssignedPlan: hasAssignedPlan,
        );

        // Save to local storage
        await saveOnboardingStatus(status);
        return status;
      }

      return const OnboardingStatus();
    } catch (e) {
      return const OnboardingStatus();
    }
  }

  // Save onboarding status
  Future<void> saveOnboardingStatus(OnboardingStatus status) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_completed_payment', status.hasCompletedPayment);
      await prefs.setBool(
        'has_completed_profile_form',
        status.hasCompletedProfileForm,
      );
      await prefs.setBool('has_seen_dashboard', status.hasSeenDashboard);
      await prefs.setBool('has_assigned_plan', status.hasAssignedPlan);
    } catch (e) {
      // Handle error silently
    }
  }

  // Mark payment as completed
  Future<void> markPaymentCompleted() async {
    final currentStatus = await getOnboardingStatus();
    final updatedStatus = currentStatus.copyWith(hasCompletedPayment: true);
    await saveOnboardingStatus(updatedStatus);
  }

  // Mark profile form as completed
  Future<void> markProfileFormCompleted() async {
    final currentStatus = await getOnboardingStatus();
    final updatedStatus = currentStatus.copyWith(hasCompletedProfileForm: true);
    await saveOnboardingStatus(updatedStatus);
  }

  // Mark dashboard as seen
  Future<void> markDashboardSeen() async {
    final currentStatus = await getOnboardingStatus();
    final updatedStatus = currentStatus.copyWith(hasSeenDashboard: true);
    await saveOnboardingStatus(updatedStatus);
  }

  // Mark plan as assigned
  Future<void> markPlanAssigned() async {
    final currentStatus = await getOnboardingStatus();
    final updatedStatus = currentStatus.copyWith(hasAssignedPlan: true);
    await saveOnboardingStatus(updatedStatus);
  }

  // Check if user has assigned plan from instructor
  Future<bool> _checkUserHasAssignedPlan(String userId) async {
    try {
      // Check if user has any active workout plan
      final workoutPlan =
          await _supabase
              .from('student_workout_plans')
              .select('id')
              .eq('student_id', userId)
              .eq('is_active', true)
              .maybeSingle();

      if (workoutPlan != null) {
        return true;
      }

      // Check if user has any active nutrition plan
      final nutritionPlan =
          await _supabase
              .from('student_nutrition_assignments')
              .select('id')
              .eq('student_id', userId)
              .eq('is_active', true)
              .maybeSingle();

      return nutritionPlan != null;
    } catch (e) {
      debugPrint('❌ Error checking assigned plan for $userId: $e');
      return false;
    }
  }

  // Update tile notification status
  List<DashboardTile> updateTileNotification(
    List<DashboardTile> tiles,
    String tileId, {
    bool? hasNotification,
    int? notificationCount,
  }) {
    return tiles.map((tile) {
      if (tile.id == tileId) {
        return tile.copyWith(
          hasNotification: hasNotification,
          notificationCount: notificationCount,
        );
      }
      return tile;
    }).toList();
  }

  // Get current user ID
  String? getCurrentUserId() {
    return _supabase.auth.currentUser?.id;
  }

  // Check if user is authenticated
  bool isUserAuthenticated() {
    return _supabase.auth.currentUser != null;
  }
}
