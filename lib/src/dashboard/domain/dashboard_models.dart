import 'package:flutter/material.dart';

// Dashboard tile model
class DashboardTile {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color? iconColor;
  final String route;
  final bool hasNotification;
  final int? notificationCount;
  final bool isEnabled;

  const DashboardTile({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    this.iconColor,
    required this.route,
    this.hasNotification = false,
    this.notificationCount,
    this.isEnabled = true,
  });

  DashboardTile copyWith({
    String? title,
    String? subtitle,
    IconData? icon,
    Color? iconColor,
    String? route,
    bool? hasNotification,
    int? notificationCount,
    bool? isEnabled,
  }) {
    return DashboardTile(
      id: id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      icon: icon ?? this.icon,
      iconColor: iconColor ?? this.iconColor,
      route: route ?? this.route,
      hasNotification: hasNotification ?? this.hasNotification,
      notificationCount: notificationCount ?? this.notificationCount,
      isEnabled: isEnabled ?? this.isEnabled,
    );
  }
}

// User subscription status
enum SubscriptionStatus {
  free('Free User'),
  premium('FitGo Premium User'),
  expired('Expired Premium');

  const SubscriptionStatus(this.displayName);
  final String displayName;

  bool get isPremium => this == SubscriptionStatus.premium;
}

// Bottom navigation tab
enum BottomNavTab {
  exercise('Exercise', Icons.fitness_center),
  nutrition('Nutrition', Icons.restaurant),
  progress('Progress', Icons.trending_up),
  explore('Explore', Icons.explore);

  const BottomNavTab(this.label, this.icon);
  final String label;
  final IconData icon;
}

// User profile info for dashboard header
class DashboardUserProfile {
  final String userId;
  final String name;
  final String? email;
  final String? avatarUrl;
  final SubscriptionStatus subscriptionStatus;
  final DateTime? subscriptionExpiryDate;

  const DashboardUserProfile({
    required this.userId,
    required this.name,
    this.email,
    this.avatarUrl,
    required this.subscriptionStatus,
    this.subscriptionExpiryDate,
  });

  String get initials {
    final nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    } else if (nameParts.isNotEmpty) {
      return nameParts[0][0].toUpperCase();
    }
    return 'U';
  }

  bool get hasAvatar => avatarUrl != null && avatarUrl!.isNotEmpty;

  DashboardUserProfile copyWith({
    String? name,
    String? email,
    String? avatarUrl,
    SubscriptionStatus? subscriptionStatus,
    DateTime? subscriptionExpiryDate,
  }) {
    return DashboardUserProfile(
      userId: userId,
      name: name ?? this.name,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      subscriptionStatus: subscriptionStatus ?? this.subscriptionStatus,
      subscriptionExpiryDate: subscriptionExpiryDate ?? this.subscriptionExpiryDate,
    );
  }
}

// Dashboard state
class DashboardState {
  final DashboardUserProfile? userProfile;
  final List<DashboardTile> tiles;
  final BottomNavTab currentTab;
  final bool isLoading;
  final String? error;

  const DashboardState({
    this.userProfile,
    this.tiles = const [],
    this.currentTab = BottomNavTab.explore,
    this.isLoading = false,
    this.error,
  });

  DashboardState copyWith({
    DashboardUserProfile? userProfile,
    List<DashboardTile>? tiles,
    BottomNavTab? currentTab,
    bool? isLoading,
    String? error,
    bool clearError = false,
  }) {
    return DashboardState(
      userProfile: userProfile ?? this.userProfile,
      tiles: tiles ?? this.tiles,
      currentTab: currentTab ?? this.currentTab,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
    );
  }
}

// Onboarding completion status
class OnboardingStatus {
  final bool hasCompletedPayment;
  final bool hasCompletedProfileForm;
  final bool hasSeenDashboard;

  const OnboardingStatus({
    this.hasCompletedPayment = false,
    this.hasCompletedProfileForm = false,
    this.hasSeenDashboard = false,
  });

  bool get isFullyOnboarded => hasCompletedPayment && hasCompletedProfileForm;
  bool get shouldShowDashboard => isFullyOnboarded;

  OnboardingStatus copyWith({
    bool? hasCompletedPayment,
    bool? hasCompletedProfileForm,
    bool? hasSeenDashboard,
  }) {
    return OnboardingStatus(
      hasCompletedPayment: hasCompletedPayment ?? this.hasCompletedPayment,
      hasCompletedProfileForm: hasCompletedProfileForm ?? this.hasCompletedProfileForm,
      hasSeenDashboard: hasSeenDashboard ?? this.hasSeenDashboard,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'has_completed_payment': hasCompletedPayment,
      'has_completed_profile_form': hasCompletedProfileForm,
      'has_seen_dashboard': hasSeenDashboard,
    };
  }

  factory OnboardingStatus.fromJson(Map<String, dynamic> json) {
    return OnboardingStatus(
      hasCompletedPayment: json['has_completed_payment'] as bool? ?? false,
      hasCompletedProfileForm: json['has_completed_profile_form'] as bool? ?? false,
      hasSeenDashboard: json['has_seen_dashboard'] as bool? ?? false,
    );
  }
}
