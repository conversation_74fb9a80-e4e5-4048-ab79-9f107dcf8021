import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/dashboard/domain/dashboard_models.dart';
import 'package:fitgo_app/src/dashboard/application/dashboard_provider.dart';
import 'package:fitgo_app/src/dashboard/presentation/components/dashboard_header.dart';
import 'package:fitgo_app/src/dashboard/presentation/components/dashboard_tiles_grid.dart';
import 'package:fitgo_app/src/dashboard/presentation/components/bottom_navigation_bar.dart';
import 'package:fitgo_app/src/dashboard/presentation/pages/placeholder_page.dart';
import 'package:fitgo_app/src/student/presentation/student_exercise_tab.dart';
import 'package:fitgo_app/src/student/presentation/student_nutrition_tab.dart';

class DashboardScreen extends HookConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardState = ref.watch(dashboardProvider);
    final currentTab = ref.watch(currentBottomNavTabProvider);

    // Initialize dashboard when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(dashboardProvider.notifier).initializeDashboard();
      });
      return null;
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      body: Column(
        children: [
          Expanded(
            child: _buildCurrentTabContent(currentTab, dashboardState),
          ),
          DashboardBottomNavigationBar(
            currentTab: currentTab,
            onTabChanged: (tab) {
              ref.read(dashboardProvider.notifier).changeTab(tab);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentTabContent(BottomNavTab currentTab, DashboardState dashboardState) {
    switch (currentTab) {
      case BottomNavTab.exercise:
        return const StudentExerciseTab();
      case BottomNavTab.nutrition:
        return const StudentNutritionTab();
      case BottomNavTab.progress:
        return PlaceholderPage(
          title: 'Progress'.hardcoded,
          icon: Icons.trending_up,
          description: 'Your fitness progress and statistics will appear here.'.hardcoded,
        );
      case BottomNavTab.explore:
        return _buildExploreTab(dashboardState);
    }
  }

  Widget _buildExploreTab(DashboardState dashboardState) {
    if (dashboardState.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
        ),
      );
    }

    if (dashboardState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            TextWidget(
              'Bir hata oluştu'.hardcoded,
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            TextWidget(
              dashboardState.error!,
              style: ATextStyle.medium.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // Retry loading dashboard
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AColor.fitgoGreen,
                foregroundColor: Colors.white,
              ),
              child: TextWidget(
                'Tekrar Dene'.hardcoded,
                style: ATextStyle.medium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return SafeArea(
      child: Column(
        children: [
          // Dashboard header
          DashboardHeader(
            userProfile: dashboardState.userProfile,
            onMenuTap: () {
              // TODO: Open drawer menu
            },
          ),
          
          // Dashboard content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome message
                  _buildWelcomeMessage(dashboardState.userProfile),
                  const SizedBox(height: 24),
                  
                  // Dashboard tiles grid
                  DashboardTilesGrid(
                    tiles: dashboardState.tiles,
                    onTileTap: (tile) {
                      _handleTileTap(tile);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeMessage(DashboardUserProfile? userProfile) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AColor.fitgoGreen.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.dashboard,
            color: AColor.fitgoGreen,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  'Hoş geldin, ${userProfile?.name.split(' ').first ?? 'Kullanıcı'}!'.hardcoded,
                  style: ATextStyle.large.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                TextWidget(
                  'Fitness yolculuğuna devam etmeye hazır mısın?'.hardcoded,
                  style: ATextStyle.medium.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleTileTap(DashboardTile tile) {
    // TODO: Navigate to tile route
    // For now, just show a snackbar
    print('Tapped tile: ${tile.title} -> ${tile.route}');
  }
}
