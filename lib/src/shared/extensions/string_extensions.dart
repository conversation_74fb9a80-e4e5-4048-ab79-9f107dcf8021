import 'package:fitgo_app/src/shared/enums/regex_type.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:flutter/material.dart';

extension ValidationExtensions on String? {
  String? isValidId(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return RegexType.id.regex.hasMatch(this!)
        ? null
        : 'Enter valid ID'.hardcoded;
  }

  String? isValidMail(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return RegexType.eMail.regex.hasMatch(this!)
        ? null
        : 'Enter Valid Mail'.hardcoded;
  }

  String? isValidPassword(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return RegexType.password.regex.hasMatch(this!)
        ? null
        : 'Enter Valid Password'.hardcoded;
  }

  String? isValidPasswordAgain(BuildContext context, String password) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    if (!RegexType.password.regex.hasMatch(this!)) {
      return 'Enter Valid Password'.hardcoded;
    }
    if (this != password) {
      return 'Şifreler eşleşmiyor'.hardcoded;
    }
    return null;
  }

  String? isValidOTP(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return RegexType.otp.regex.hasMatch(this!)
        ? null
        : 'Enter Valid OTP'.hardcoded;
  }

  String? isValidPostalCode(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return RegexType.postalCode.regex.hasMatch(this!)
        ? null
        : 'Enter Valid Postal Code'.hardcoded;
  }

  String? isValidName(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return RegexType.name.regex.hasMatch(this!)
        ? null
        : 'Enter Valid Name'.hardcoded;
  }

  String? isValidPhone(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return RegexType.phone.regex.hasMatch(this!)
        ? null
        : 'Enter Valid Phone'.hardcoded;
  }

  String? isValidField(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return null;
  }

  String? isValidWebAddress(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }
    return RegexType.webAddress.regex.hasMatch(this!)
        ? null
        : 'Enter Valid Web Address'.hardcoded;
  }

  String? isValidBirthDate(BuildContext context) {
    if (this == null || this!.trim().isEmpty) {
      return 'Field Required'.hardcoded;
    }

    final dateText = this!.trim();

    // Check format DD/MM/YYYY
    final dateRegex = RegExp(r'^(\d{2})\/(\d{2})\/(\d{4})$');
    if (!dateRegex.hasMatch(dateText)) {
      return 'Enter date in DD/MM/YYYY format'.hardcoded;
    }

    try {
      final parts = dateText.split('/');
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);

      // Basic validation
      if (month < 1 || month > 12) {
        return 'Invalid month'.hardcoded;
      }

      if (day < 1 || day > 31) {
        return 'Invalid day'.hardcoded;
      }

      // Create date to validate
      final date = DateTime(year, month, day);

      // Check if date is valid (handles leap years, etc.)
      if (date.day != day || date.month != month || date.year != year) {
        return 'Invalid date'.hardcoded;
      }

      // Check if date is not in the future
      if (date.isAfter(DateTime.now())) {
        return 'Birth date cannot be in the future'.hardcoded;
      }

      // Check if date is reasonable (not too old)
      final minDate = DateTime.now().subtract(
        const Duration(days: 365 * 120),
      ); // 120 years ago
      if (date.isBefore(minDate)) {
        return 'Invalid birth date'.hardcoded;
      }

      return null;
    } catch (e) {
      return 'Invalid date format'.hardcoded;
    }
  }
}
