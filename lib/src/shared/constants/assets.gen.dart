/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Lato-Regular.ttf
  String get latoRegular => 'assets/fonts/Lato-Regular.ttf';

  /// File path: assets/fonts/Oswald-VariableFont_wght.ttf
  String get oswaldVariableFontWght => 'assets/fonts/Oswald-VariableFont_wght.ttf';

  /// File path: assets/fonts/Poppins-Regular.ttf
  String get poppinsRegular => 'assets/fonts/Poppins-Regular.ttf';

  /// File path: assets/fonts/Roboto-VariableFont_wdth,wght.ttf
  String get robotoVariableFontWdthWght => 'assets/fonts/Roboto-VariableFont_wdth,wght.ttf';

  /// List of all assets
  List<String> get values => [latoRegular, oswaldVariableFontWght, poppinsRegular, robotoVariableFontWdthWght];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/bottom_exercise_icon.svg
  SvgGenImage get bottomExerciseIcon => const SvgGenImage('assets/icons/bottom_exercise_icon.svg');

  /// File path: assets/icons/bottom_explore_icon.svg
  SvgGenImage get bottomExploreIcon => const SvgGenImage('assets/icons/bottom_explore_icon.svg');

  /// File path: assets/icons/bottom_home_icon.svg
  SvgGenImage get bottomHomeIcon => const SvgGenImage('assets/icons/bottom_home_icon.svg');

  /// File path: assets/icons/bottom_members_icon.svg
  SvgGenImage get bottomMembersIcon => const SvgGenImage('assets/icons/bottom_members_icon.svg');

  /// File path: assets/icons/bottom_nutrition_icon.svg
  SvgGenImage get bottomNutritionIcon => const SvgGenImage('assets/icons/bottom_nutrition_icon.svg');

  /// File path: assets/icons/bottom_profile_icon.svg
  SvgGenImage get bottomProfileIcon => const SvgGenImage('assets/icons/bottom_profile_icon.svg');

  /// File path: assets/icons/bottom_progress_icon.svg
  SvgGenImage get bottomProgressIcon => const SvgGenImage('assets/icons/bottom_progress_icon.svg');

  /// File path: assets/icons/language_icon.svg
  SvgGenImage get languageIcon => const SvgGenImage('assets/icons/language_icon.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    bottomExerciseIcon,
    bottomExploreIcon,
    bottomHomeIcon,
    bottomMembersIcon,
    bottomNutritionIcon,
    bottomProfileIcon,
    bottomProgressIcon,
    languageIcon,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/fitgo_app_logo.png
  AssetGenImage get fitgoAppLogo => const AssetGenImage('assets/images/fitgo_app_logo.png');

  /// File path: assets/images/fitgo_logo.png
  AssetGenImage get fitgoLogo => const AssetGenImage('assets/images/fitgo_logo.png');

  /// File path: assets/images/instructorOnboarding1.png
  AssetGenImage get instructorOnboarding1 => const AssetGenImage('assets/images/instructorOnboarding1.png');

  /// File path: assets/images/instructorOnboarding2.png
  AssetGenImage get instructorOnboarding2 => const AssetGenImage('assets/images/instructorOnboarding2.png');

  /// File path: assets/images/instructorOnboarding3.png
  AssetGenImage get instructorOnboarding3 => const AssetGenImage('assets/images/instructorOnboarding3.png');

  /// File path: assets/images/onboardingBackground.png
  AssetGenImage get onboardingBackground => const AssetGenImage('assets/images/onboardingBackground.png');

  /// File path: assets/images/studentOnboarding1.png
  AssetGenImage get studentOnboarding1 => const AssetGenImage('assets/images/studentOnboarding1.png');

  /// File path: assets/images/studentOnboarding2.png
  AssetGenImage get studentOnboarding2 => const AssetGenImage('assets/images/studentOnboarding2.png');

  /// File path: assets/images/studentOnboarding3.png
  AssetGenImage get studentOnboarding3 => const AssetGenImage('assets/images/studentOnboarding3.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    fitgoAppLogo,
    fitgoLogo,
    instructorOnboarding1,
    instructorOnboarding2,
    instructorOnboarding3,
    onboardingBackground,
    studentOnboarding1,
    studentOnboarding2,
    studentOnboarding3,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(_assetName, assetBundle: bundle, packageName: package);
    } else {
      loader = _svg.SvgAssetLoader(_assetName, assetBundle: bundle, packageName: package, theme: theme);
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ?? (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
