import 'package:country_code_picker/country_code_picker.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/enums/regex_type.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PhoneInputField extends HookConsumerWidget {
  const PhoneInputField({
    super.key,
    required this.controller,
    required this.onCountryChanged,
    this.headerText,
    this.validator,
    this.textInputAction,
    this.enabled = true,
    this.initialCountryCode = 'TR',
  });

  final TextEditingController controller;
  final Function(CountryCode) onCountryChanged;
  final String? headerText;
  final String? Function(String?)? validator;
  final TextInputAction? textInputAction;
  final bool enabled;
  final String initialCountryCode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final focusNode = useFocusNode();
    final hasError = useState<bool>(false);
    final errorText = useState<String?>(null);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (headerText != null) ...[
          TextWidget(
            headerText!,
            style: ATextStyle.medium.copyWith(
              color: AColor.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: AColor.white,
            border: Border.all(
              color: hasError.value ? AColor.red : AColor.fitgoColor3,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Country Code Picker
              CountryCodePicker(
                onChanged: (countryCode) {
                  onCountryChanged(countryCode);
                },
                initialSelection: initialCountryCode,
                favorite: const ['+90', 'TR'],
                showCountryOnly: false,
                showOnlyCountryWhenClosed: false,
                alignLeft: false,
                textStyle: ATextStyle.medium.copyWith(
                  color: AColor.black,
                  fontSize: 16,
                ),
                dialogTextStyle: ATextStyle.medium.copyWith(
                  color: AColor.black,
                  fontSize: 16,
                ),
                searchStyle: ATextStyle.medium.copyWith(
                  color: AColor.black,
                  fontSize: 16,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8),
                flagDecoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                ),
                backgroundColor: AColor.white,
                barrierColor: Colors.black54,
                dialogBackgroundColor: AColor.white,
                closeIcon: const Icon(Icons.close, color: AColor.black),
              ),
              // Vertical divider
              Container(
                height: 48,
                width: 1,
                color: AColor.grey.withValues(alpha: 0.3),
              ),
              // Phone number input
              Expanded(
                child: TextFormField(
                  controller: controller,
                  focusNode: focusNode,
                  enabled: enabled,
                  keyboardType: TextInputType.phone,
                  textInputAction: textInputAction,
                  style: ATextStyle.medium.copyWith(
                    color: AColor.black,
                    fontSize: 16,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9\s-]')),
                  ],
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding: const EdgeInsets.all(12),
                    hintText: '5XX XXX XX XX',
                    hintStyle: ATextStyle.medium.copyWith(
                      color: AColor.grey,
                      fontSize: 16,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    filled: true,
                    fillColor: AColor.white,
                  ),
                  validator: (value) {
                    final result = validator?.call(value);
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      hasError.value = result != null;
                      errorText.value = result;
                    });
                    return result;
                  },
                ),
              ),
            ],
          ),
        ),
        if (hasError.value && errorText.value != null) ...[
          const SizedBox(height: 4),
          TextWidget(
            errorText.value!,
            style: ATextStyle.small.copyWith(color: AColor.red, fontSize: 12),
          ),
        ],
      ],
    );
  }
}
