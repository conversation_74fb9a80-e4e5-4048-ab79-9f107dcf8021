import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/shared/providers/supabase_provider.dart';

class AppDrawer extends ConsumerWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final supabaseService = ref.watch(supabaseServiceProvider);
    final user = supabaseService.client.auth.currentUser;

    return Drawer(
      backgroundColor: const Color(0xFF1F2937),
      child: <PERSON><PERSON><PERSON>(
        child: Column(
          children: [
            // Header with user profile
            Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Language selector
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF374151),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: TextWidget(
                          'EN',
                          style: ATextStyle.small.copyWith(
                            color: AColor.textColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // User avatar
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: AColor.fitgoGreen, width: 3),
                      image:
                          user?.userMetadata?['avatar_url'] != null
                              ? DecorationImage(
                                image: NetworkImage(
                                  user!.userMetadata!['avatar_url'],
                                ),
                                fit: BoxFit.cover,
                              )
                              : null,
                    ),
                    child:
                        user?.userMetadata?['avatar_url'] == null
                            ? const Icon(
                              Icons.person,
                              size: 40,
                              color: AColor.textColor,
                            )
                            : null,
                  ),
                ],
              ),
            ),

            // Menu items
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: [
                  _buildMenuItem(
                    icon: Icons.person_outline,
                    title: 'Profile'.hardcoded,
                    onTap: () {
                      Navigator.pop(context);
                      // Navigate to profile page
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.star_outline,
                    title: 'Membership'.hardcoded,
                    onTap: () {
                      Navigator.pop(context);
                      // Navigate to membership page
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.notifications_outlined,
                    title: 'Notifications'.hardcoded,
                    onTap: () {
                      Navigator.pop(context);
                      // Navigate to notifications page
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.security_outlined,
                    title: 'Privacy and Security'.hardcoded,
                    onTap: () {
                      Navigator.pop(context);
                      // Navigate to privacy page
                    },
                  ),
                  const SizedBox(height: 24),
                  _buildMenuItem(
                    icon: Icons.logout_outlined,
                    title: 'Sign Out'.hardcoded,
                    onTap: () => _showLogoutDialog(context, ref),
                    isDestructive: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? Colors.red : AColor.textColor,
          size: 24,
        ),
        title: TextWidget(
          title,
          style: ATextStyle.medium.copyWith(
            color: isDestructive ? Colors.red : AColor.textColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        tileColor: Colors.transparent,
        hoverColor: const Color(0xFF374151),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1F2937),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: TextWidget(
            'Sign Out'.hardcoded,
            style: ATextStyle.large.copyWith(
              color: AColor.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: TextWidget(
            'Are you sure you want to sign out?'.hardcoded,
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: TextWidget(
                'Cancel'.hardcoded,
                style: ATextStyle.medium.copyWith(color: AColor.textColor),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context); // Close dialog
                Navigator.pop(context); // Close drawer

                // Sign out
                await ref.read(authNotifierProvider.notifier).signOut();

                // Navigate to login
                if (context.mounted) {
                  context.go('/login');
                }
              },
              child: TextWidget(
                'Sign Out'.hardcoded,
                style: ATextStyle.medium.copyWith(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
