import 'package:fitgo_app/src/program_enrollment/domain/enrollment_package.dart';
import 'package:fitgo_app/src/program_enrollment/domain/enrollment_state.dart';

// Repository for enrollment data following DDD pattern
class EnrollmentRepository {
  
  // Get available packages for a trainer
  Future<List<EnrollmentPackage>> getAvailablePackages(String trainerId) async {
    // Simulate API call - in real app this would fetch from Supabase
    await Future.delayed(const Duration(milliseconds: 800));
    
    return [
      EnrollmentPackage(
        id: 'standard',
        name: 'standard',
        displayName: 'Standard Paket',
        type: PackageType.standard,
        features: [
          const PackageFeature(
            id: 'personal_training',
            title: '<PERSON><PERSON><PERSON><PERSON> Antrenman Programı',
            icon: '💪',
          ),
          const PackageFeature(
            id: 'nutrition_guide',
            title: 'Beslenme Rehberi',
            icon: '🥗',
          ),
          const PackageFeature(
            id: 'progress_tracking',
            title: '<PERSON><PERSON><PERSON><PERSON>',
            icon: '📊',
          ),
          const PackageFeature(
            id: 'weekly_checkin',
            title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            icon: '📅',
          ),
          const PackageFeature(
            id: 'email_support',
            title: 'E-posta Des<PERSON>ğ<PERSON>',
            icon: '📧',
          ),
        ],
      ),
      EnrollmentPackage(
        id: 'premium',
        name: 'premium',
        displayName: 'FitGo Premium ile',
        type: PackageType.premium,
        isPopular: true,
        features: [
          const PackageFeature(
            id: 'personal_training',
            title: 'Kişisel Antrenman Programı',
            icon: '👑',
            isHighlight: true,
          ),
          const PackageFeature(
            id: 'premium_nutrition',
            title: 'Premium Beslenme Planı',
            icon: '🍽️',
            isHighlight: true,
          ),
          const PackageFeature(
            id: 'live_sessions',
            title: 'Canlı Video Seansları',
            icon: '📹',
            isNew: true,
          ),
          const PackageFeature(
            id: 'daily_checkin',
            title: 'Günlük Kontrol & Motivasyon',
            icon: '⚡',
            isHighlight: true,
          ),
          const PackageFeature(
            id: 'priority_support',
            title: '7/24 Öncelikli Destek',
            icon: '🚀',
            isHighlight: true,
          ),
          const PackageFeature(
            id: 'supplement_guide',
            title: 'Takviye Rehberi',
            icon: '💊',
          ),
          const PackageFeature(
            id: 'meal_planning',
            title: 'Özel Yemek Planlaması',
            icon: '📋',
            isNew: true,
          ),
          const PackageFeature(
            id: 'body_analysis',
            title: 'Detaylı Vücut Analizi',
            icon: '🔬',
            isHighlight: true,
          ),
        ],
      ),
    ];
  }

  // Get pricing for packages
  Future<Map<String, List<PackagePricing>>> getPackagePricing() async {
    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 500));
    
    return {
      'standard': [
        const PackagePricing(
          packageId: 'standard',
          duration: DurationOption.oneMonth,
          originalPrice: 350.0,
        ),
        const PackagePricing(
          packageId: 'standard',
          duration: DurationOption.threeMonths,
          originalPrice: 1000.0,
          discountedPrice: 900.0,
          discountPercentage: 10.0,
        ),
        const PackagePricing(
          packageId: 'standard',
          duration: DurationOption.sixMonths,
          originalPrice: 2000.0,
          discountedPrice: 1700.0,
          discountPercentage: 15.0,
        ),
        const PackagePricing(
          packageId: 'standard',
          duration: DurationOption.oneYear,
          originalPrice: 4000.0,
          discountedPrice: 3200.0,
          discountPercentage: 20.0,
        ),
      ],
      'premium': [
        const PackagePricing(
          packageId: 'premium',
          duration: DurationOption.oneMonth,
          originalPrice: 500.0,
        ),
        const PackagePricing(
          packageId: 'premium',
          duration: DurationOption.threeMonths,
          originalPrice: 1450.0,
          discountedPrice: 1300.0,
          discountPercentage: 10.0,
        ),
        const PackagePricing(
          packageId: 'premium',
          duration: DurationOption.sixMonths,
          originalPrice: 2900.0,
          discountedPrice: 2400.0,
          discountPercentage: 17.0,
        ),
        const PackagePricing(
          packageId: 'premium',
          duration: DurationOption.oneYear,
          originalPrice: 5800.0,
          discountedPrice: 4500.0,
          discountPercentage: 22.0,
        ),
      ],
    };
  }

  // Validate promo code
  Future<PromoCodeValidationResult> validatePromoCode(String code) async {
    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 1000));
    
    // Mock promo codes for demo
    final mockPromoCodes = {
      'FITGO10': const PromoCode(
        code: 'FITGO10',
        description: '10% indirim',
        discountPercentage: 10.0,
        isValid: true,
      ),
      'WELCOME50': const PromoCode(
        code: 'WELCOME50',
        description: '50₺ indirim',
        discountAmount: 50.0,
        isValid: true,
      ),
      'PREMIUM20': const PromoCode(
        code: 'PREMIUM20',
        description: '20% premium indirim',
        discountPercentage: 20.0,
        isValid: true,
      ),
    };

    final promoCode = mockPromoCodes[code.toUpperCase()];
    
    if (promoCode != null) {
      return PromoCodeValidationResult.success(promoCode);
    } else {
      return PromoCodeValidationResult.error('Promosyon kodu bulunamadı');
    }
  }

  // Submit enrollment
  Future<bool> submitEnrollment({
    required String trainerId,
    required String packageId,
    required DurationOption duration,
    String? promoCode,
  }) async {
    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 1500));
    
    // Mock success for demo
    return true;
  }
}
