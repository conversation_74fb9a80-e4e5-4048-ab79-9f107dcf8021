import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Application state providers following Riverpod best practices

/// Current user type selection state
final currentUserTypeProvider = StateProvider<UserType?>((ref) => null);

/// Onboarding completion state
final onboardingCompletedProvider = StateProvider<bool>((ref) => false);

/// Authentication state provider
final isAuthenticatedProvider = StateProvider<bool>((ref) => false);

/// User session provider
final userSessionProvider = StateProvider<Map<String, dynamic>?>((ref) => null);

/// App theme mode provider
final themeModeProvider = StateProvider<ThemeMode>((ref) => ThemeMode.system);

/// Student enrollment status provider
final studentEnrollmentStatusProvider = StateProvider<bool>((ref) => false);
