import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import '../../domain/instructor_profile_models.dart';
import '../../application/instructor_profile_provider.dart';

/// Basic info edit dialog
class BasicInfoEditDialog extends HookConsumerWidget {
  final InstructorBasicInfo basicInfo;

  const BasicInfoEditDialog({
    super.key,
    required this.basicInfo,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nameController = useTextEditingController(text: basicInfo.name);
    final titleController = useTextEditingController(text: basicInfo.title);
    final photoUrlController = useTextEditingController(text: basicInfo.profilePictureUrl ?? '');
    final experienceController = useTextEditingController(text: basicInfo.experienceYears.toString());

    return AlertDialog(
      backgroundColor: const Color(0xFF1F2937),
      title: Text(
        'Edit Basic Information',
        style: ATextStyle.title.copyWith(color: Colors.white),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTextField(
              controller: nameController,
              label: 'Full Name',
              hint: 'Enter your full name',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: titleController,
              label: 'Professional Title',
              hint: 'e.g., Elite Fitness Trainer',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: photoUrlController,
              label: 'Profile Photo URL',
              hint: 'https://example.com/photo.jpg',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: experienceController,
              label: 'Years of Experience',
              hint: 'Enter years',
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: ATextStyle.medium.copyWith(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            final updatedInfo = InstructorBasicInfo(
              id: basicInfo.id,
              name: nameController.text.trim(),
              title: titleController.text.trim(),
              profilePictureUrl: photoUrlController.text.trim().isEmpty 
                  ? null 
                  : photoUrlController.text.trim(),
              averageRating: basicInfo.averageRating,
              totalReviews: basicInfo.totalReviews,
              experienceYears: int.tryParse(experienceController.text) ?? basicInfo.experienceYears,
              totalStudents: basicInfo.totalStudents,
              primaryCertification: basicInfo.primaryCertification,
              canEdit: basicInfo.canEdit,
            );

            await ref.read(instructorProfileNotifierProvider.notifier)
                .handleEditAction(
                  ProfileEditAction.editBasicInfo,
                  data: {'basicInfo': updatedInfo},
                );

            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
          ),
          child: const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: ATextStyle.medium.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: const Color(0xFF374151),
          ),
        ),
      ],
    );
  }
}

/// Work experience edit dialog
class WorkExperienceEditDialog extends HookConsumerWidget {
  final WorkExperience? experience;

  const WorkExperienceEditDialog({
    super.key,
    this.experience,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final companyController = useTextEditingController(text: experience?.companyName ?? '');
    final roleController = useTextEditingController(text: experience?.role ?? '');
    final startYearController = useTextEditingController(text: experience?.startYear ?? '');
    final endYearController = useTextEditingController(text: experience?.endYear ?? '');
    final descriptionController = useTextEditingController(text: experience?.description ?? '');
    final isCurrent = useState(experience?.isCurrent ?? false);

    return AlertDialog(
      backgroundColor: const Color(0xFF1F2937),
      title: Text(
        experience == null ? 'Add Work Experience' : 'Edit Work Experience',
        style: ATextStyle.title.copyWith(color: Colors.white),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTextField(
              controller: companyController,
              label: 'Company/Gym Name',
              hint: 'e.g., Elite Fitness Club',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: roleController,
              label: 'Role/Position',
              hint: 'e.g., Senior Trainer',
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: startYearController,
                    label: 'Start Year',
                    hint: '2020',
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTextField(
                    controller: endYearController,
                    label: 'End Year',
                    hint: 'Present',
                    enabled: !isCurrent.value,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: isCurrent.value,
                  onChanged: (value) => isCurrent.value = value ?? false,
                  activeColor: const Color(0xFFFACC15),
                ),
                Text(
                  'Currently working here',
                  style: ATextStyle.medium.copyWith(color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: descriptionController,
              label: 'Description',
              hint: 'Describe your role and responsibilities...',
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: ATextStyle.medium.copyWith(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            final newExperience = WorkExperience(
              id: experience?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
              companyName: companyController.text.trim(),
              role: roleController.text.trim(),
              startYear: startYearController.text.trim(),
              endYear: isCurrent.value ? null : endYearController.text.trim(),
              description: descriptionController.text.trim(),
              isCurrent: isCurrent.value,
            );

            await ref.read(instructorProfileNotifierProvider.notifier)
                .handleEditAction(
                  experience == null 
                      ? ProfileEditAction.addWorkExperience 
                      : ProfileEditAction.editWorkExperience,
                  data: {'workExperience': newExperience},
                );

            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
          ),
          child: Text(experience == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: ATextStyle.medium.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          enabled: enabled,
          style: TextStyle(color: enabled ? Colors.white : Colors.grey),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
              borderRadius: BorderRadius.circular(8),
            ),
            disabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF4B5563)),
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: enabled ? const Color(0xFF374151) : const Color(0xFF4B5563),
          ),
        ),
      ],
    );
  }
}

/// Certification edit dialog
class CertificationEditDialog extends HookConsumerWidget {
  final Certification? certification;

  const CertificationEditDialog({
    super.key,
    this.certification,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nameController = useTextEditingController(text: certification?.name ?? '');
    final issuerController = useTextEditingController(text: certification?.issuer ?? '');
    final yearController = useTextEditingController(text: certification?.year ?? '');
    final linkController = useTextEditingController(text: certification?.externalLink ?? '');

    return AlertDialog(
      backgroundColor: const Color(0xFF1F2937),
      title: Text(
        certification == null ? 'Add Certification' : 'Edit Certification',
        style: ATextStyle.title.copyWith(color: Colors.white),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTextField(
              controller: nameController,
              label: 'Certification Name',
              hint: 'e.g., NASM CPT',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: issuerController,
              label: 'Issuing Organization',
              hint: 'e.g., National Academy of Sports Medicine',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: yearController,
              label: 'Year Obtained',
              hint: '2020',
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: linkController,
              label: 'Verification Link (Optional)',
              hint: 'https://verify.example.com',
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: ATextStyle.medium.copyWith(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            final newCertification = Certification(
              id: certification?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
              name: nameController.text.trim(),
              issuer: issuerController.text.trim(),
              year: yearController.text.trim(),
              externalLink: linkController.text.trim().isEmpty 
                  ? null 
                  : linkController.text.trim(),
              isVerified: certification?.isVerified ?? false,
            );

            await ref.read(instructorProfileNotifierProvider.notifier)
                .handleEditAction(
                  certification == null 
                      ? ProfileEditAction.addCertification 
                      : ProfileEditAction.editCertification,
                  data: {'certification': newCertification},
                );

            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
          ),
          child: Text(certification == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: ATextStyle.medium.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: const Color(0xFF374151),
          ),
        ),
      ],
    );
  }
}

/// FAQ edit dialog
class FAQEditDialog extends HookConsumerWidget {
  final FAQ? faq;

  const FAQEditDialog({
    super.key,
    this.faq,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final questionController = useTextEditingController(text: faq?.question ?? '');
    final answerController = useTextEditingController(text: faq?.answer ?? '');

    return AlertDialog(
      backgroundColor: const Color(0xFF1F2937),
      title: Text(
        faq == null ? 'Add FAQ' : 'Edit FAQ',
        style: ATextStyle.title.copyWith(color: Colors.white),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTextField(
              controller: questionController,
              label: 'Question',
              hint: 'What is your training style?',
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: answerController,
              label: 'Answer',
              hint: 'Provide a detailed answer...',
              maxLines: 4,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: ATextStyle.medium.copyWith(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            final newFAQ = FAQ(
              id: faq?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
              question: questionController.text.trim(),
              answer: answerController.text.trim(),
              order: faq?.order ?? 0,
              isExpanded: faq?.isExpanded ?? false,
            );

            await ref.read(instructorProfileNotifierProvider.notifier)
                .handleEditAction(
                  faq == null 
                      ? ProfileEditAction.addFAQ 
                      : ProfileEditAction.editFAQ,
                  data: {'faq': newFAQ},
                );

            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFFACC15),
            foregroundColor: Colors.black,
          ),
          child: Text(faq == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: ATextStyle.medium.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.grey),
            border: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFF374151)),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Color(0xFFFACC15)),
              borderRadius: BorderRadius.circular(8),
            ),
            filled: true,
            fillColor: const Color(0xFF374151),
          ),
        ),
      ],
    );
  }
}
