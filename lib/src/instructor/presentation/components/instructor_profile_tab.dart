import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import '../../application/instructor_profile_provider.dart';
import '../../domain/instructor_profile_models.dart';
import '../subscription_plan_management_screen.dart';

class InstructorProfileTab extends HookConsumerWidget {
  const InstructorProfileTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileState = ref.watch(instructorProfileNotifierProvider);

    // Initialize profile when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(instructorProfileNotifierProvider.notifier).initializeProfile();
      });
      return null;
    }, []);

    return SafeArea(
      child: profileState.isLoading
          ? const Center(child: CircularProgressIndicator(color: Color(0xFFFACC15)))
          : profileState.error != null
              ? _buildErrorState(ref, profileState.error!)
              : profileState.profileData != null
                  ? _buildProfileContent(context, ref, profileState.profileData!)
                  : const Center(
                      child: Text(
                        'No profile data available',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
    );
  }

  Widget _buildErrorState(WidgetRef ref, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 48),
          const SizedBox(height: 16),
          Text(
            'Error loading profile',
            style: ATextStyle.title.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: ATextStyle.medium.copyWith(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.read(instructorProfileNotifierProvider.notifier).refreshProfile(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFACC15),
              foregroundColor: Colors.black,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent(BuildContext context, WidgetRef ref, InstructorProfileData profileData) {
    return RefreshIndicator(
      onRefresh: () => ref.read(instructorProfileNotifierProvider.notifier).refreshProfile(),
      color: const Color(0xFFFACC15),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeaderSection(context, ref, profileData.basicInfo),
            const SizedBox(height: 24),

            // Work History Section
            _buildWorkHistorySection(context, ref, profileData.workHistory),
            const SizedBox(height: 24),

            // Certifications Section
            _buildCertificationsSection(context, ref, profileData.certifications),
            const SizedBox(height: 24),

            // Client Reviews Section
            _buildReviewsSection(context, ref, profileData.reviews),
            const SizedBox(height: 24),

            // FAQs Section
            _buildFAQsSection(context, ref, profileData.faqs),
            const SizedBox(height: 24),

            // Manage Subscription Button
            _buildSubscriptionButton(context, ref, profileData.subscriptionInfo),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// Build header section with profile info and badges
  Widget _buildHeaderSection(BuildContext context, WidgetRef ref, InstructorBasicInfo basicInfo) {
    final badges = ref.watch(instructorBadgesProvider);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Column(
        children: [
          // Profile Picture and Basic Info
          Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: const Color(0xFFFACC15),
                    backgroundImage: basicInfo.profilePictureUrl != null
                        ? NetworkImage(basicInfo.profilePictureUrl!)
                        : null,
                    child: basicInfo.profilePictureUrl == null
                        ? Text(
                            basicInfo.name.substring(0, 1).toUpperCase(),
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  if (basicInfo.canEdit)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () => ref.read(instructorProfileNotifierProvider.notifier)
                            .showEditDialog(context, ProfileEditAction.editBasicInfo),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Color(0xFFFACC15),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.edit, size: 16, color: Colors.black),
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      basicInfo.name,
                      style: ATextStyle.title.copyWith(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      basicInfo.title,
                      style: ATextStyle.medium.copyWith(
                        color: const Color(0xFFFACC15),
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Rating Row
                    Row(
                      children: [
                        const Icon(Icons.star, color: Color(0xFFFACC15), size: 20),
                        const SizedBox(width: 4),
                        Text(
                          basicInfo.averageRating.toStringAsFixed(1),
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '(${basicInfo.totalReviews} reviews)',
                          style: ATextStyle.small.copyWith(color: Colors.grey),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Badges
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: badges.map((badge) => _buildBadge(badge)).toList(),
          ),
        ],
      ),
    );
  }

  /// Build badge widget
  Widget _buildBadge(InstructorBadge badge) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Color(int.parse(badge.type.color.substring(1), radix: 16) + 0xFF000000).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Color(int.parse(badge.type.color.substring(1), radix: 16) + 0xFF000000).withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(badge.icon, style: const TextStyle(fontSize: 14)),
          const SizedBox(width: 6),
          Text(
            badge.value,
            style: ATextStyle.small.copyWith(
              color: Color(int.parse(badge.type.color.substring(1), radix: 16) + 0xFF000000),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build work history section
  Widget _buildWorkHistorySection(BuildContext context, WidgetRef ref, List<WorkExperience> workHistory) {
    return _buildSection(
      title: 'Work History',
      isEmpty: workHistory.isEmpty,
      emptyMessage: 'Add your work experience to showcase your professional background',
      onAdd: () => ref.read(instructorProfileNotifierProvider.notifier)
          .showEditDialog(context, ProfileEditAction.addWorkExperience),
      child: Column(
        children: workHistory.map((experience) => _buildWorkExperienceCard(context, ref, experience)).toList(),
      ),
    );
  }

  /// Build work experience card
  Widget _buildWorkExperienceCard(BuildContext context, WidgetRef ref, WorkExperience experience) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        experience.companyName,
                        style: ATextStyle.medium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    if (experience.isCurrent)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: const Color(0xFF10B981),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Current',
                          style: ATextStyle.small.copyWith(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  experience.role,
                  style: ATextStyle.medium.copyWith(color: const Color(0xFFFACC15)),
                ),
                const SizedBox(height: 4),
                Text(
                  experience.duration,
                  style: ATextStyle.small.copyWith(color: Colors.grey),
                ),
                const SizedBox(height: 8),
                Text(
                  experience.description,
                  style: ATextStyle.small.copyWith(color: Colors.grey[300]),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => ref.read(instructorProfileNotifierProvider.notifier)
                .showEditDialog(context, ProfileEditAction.editWorkExperience, data: experience),
            icon: const Icon(Icons.edit, color: Colors.grey, size: 20),
          ),
        ],
      ),
    );
  }

  /// Build certifications section
  Widget _buildCertificationsSection(BuildContext context, WidgetRef ref, List<Certification> certifications) {
    return _buildSection(
      title: 'Certifications',
      isEmpty: certifications.isEmpty,
      emptyMessage: 'Add certifications to build trust with potential clients',
      onAdd: () => ref.read(instructorProfileNotifierProvider.notifier)
          .showEditDialog(context, ProfileEditAction.addCertification),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
        ),
        itemCount: certifications.length,
        itemBuilder: (context, index) => _buildCertificationCard(context, ref, certifications[index]),
      ),
    );
  }

  /// Build certification card
  Widget _buildCertificationCard(BuildContext context, WidgetRef ref, Certification certification) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  certification.name,
                  style: ATextStyle.medium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (certification.isVerified)
                const Icon(Icons.verified, color: Color(0xFF10B981), size: 16),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            certification.issuer,
            style: ATextStyle.small.copyWith(color: const Color(0xFFFACC15)),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            certification.year,
            style: ATextStyle.small.copyWith(color: Colors.grey),
          ),
          const Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (certification.externalLink != null)
                const Icon(Icons.link, color: Colors.grey, size: 16),
              IconButton(
                onPressed: () => ref.read(instructorProfileNotifierProvider.notifier)
                    .showEditDialog(context, ProfileEditAction.editCertification, data: certification),
                icon: const Icon(Icons.edit, color: Colors.grey, size: 16),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build reviews section
  Widget _buildReviewsSection(BuildContext context, WidgetRef ref, List<ClientReview> reviews) {
    return _buildSection(
      title: 'Client Reviews',
      isEmpty: reviews.isEmpty,
      emptyMessage: 'Client reviews will appear here once you start training students',
      onAdd: null, // Reviews can't be added by instructor
      child: Column(
        children: reviews.take(3).map((review) => _buildReviewCard(context, ref, review)).toList(),
      ),
    );
  }

  /// Build review card
  Widget _buildReviewCard(BuildContext context, WidgetRef ref, ClientReview review) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFFFACC15),
                backgroundImage: review.clientAvatarUrl != null
                    ? NetworkImage(review.clientAvatarUrl!)
                    : null,
                child: review.clientAvatarUrl == null
                    ? Text(
                        review.clientName.substring(0, 1).toUpperCase(),
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.clientName,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Row(
                      children: [
                        ...List.generate(5, (index) {
                          return Icon(
                            index < review.rating ? Icons.star : Icons.star_border,
                            color: const Color(0xFFFACC15),
                            size: 16,
                          );
                        }),
                        const SizedBox(width: 8),
                        Text(
                          _formatTimeAgo(review.createdAt),
                          style: ATextStyle.small.copyWith(color: Colors.grey),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (review.canModerate)
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.grey, size: 20),
                  onSelected: (value) {
                    if (value == 'delete') {
                      ref.read(instructorProfileNotifierProvider.notifier)
                          .handleEditAction(ProfileEditAction.deleteReview, data: {'reviewId': review.id});
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'delete',
                      child: Text('Delete Review'),
                    ),
                  ],
                ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            review.comment,
            style: ATextStyle.small.copyWith(color: Colors.grey[300]),
          ),
        ],
      ),
    );
  }

  /// Build FAQs section
  Widget _buildFAQsSection(BuildContext context, WidgetRef ref, List<FAQ> faqs) {
    final profileState = ref.watch(instructorProfileNotifierProvider);
    final expandedFAQs = profileState.expandedFAQs;

    return _buildSection(
      title: 'Frequently Asked Questions',
      isEmpty: faqs.isEmpty,
      emptyMessage: 'Add frequently asked questions to help potential clients learn more about you',
      onAdd: () => ref.read(instructorProfileNotifierProvider.notifier)
          .showEditDialog(context, ProfileEditAction.addFAQ),
      child: Column(
        children: expandedFAQs.map((faq) => _buildFAQCard(context, ref, faq)).toList(),
      ),
    );
  }

  /// Build FAQ card
  Widget _buildFAQCard(BuildContext context, WidgetRef ref, FAQ faq) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4B5563)),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () => ref.read(instructorProfileNotifierProvider.notifier).toggleFAQ(faq.id),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      faq.question,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Icon(
                    faq.isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: const Color(0xFFFACC15),
                  ),
                  IconButton(
                    onPressed: () => ref.read(instructorProfileNotifierProvider.notifier)
                        .showEditDialog(context, ProfileEditAction.editFAQ, data: faq),
                    icon: const Icon(Icons.edit, color: Colors.grey, size: 20),
                  ),
                ],
              ),
            ),
          ),
          if (faq.isExpanded)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Text(
                faq.answer,
                style: ATextStyle.small.copyWith(color: Colors.grey[300]),
              ),
            ),
        ],
      ),
    );
  }

  /// Build subscription button
  Widget _buildSubscriptionButton(BuildContext context, WidgetRef ref, SubscriptionInfo? subscriptionInfo) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFACC15), Color(0xFFF59E0B)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFACC15).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Submit for Review',
            style: ATextStyle.title.copyWith(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Configure your subscription plans and submit for admin approval to start coaching students',
            style: ATextStyle.medium.copyWith(
              color: Colors.black87,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          if (subscriptionInfo != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '${subscriptionInfo.planName} - \$${subscriptionInfo.monthlyPrice}/month',
                style: ATextStyle.small.copyWith(
                  color: Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _navigateToSubscriptionManagement(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: const Color(0xFFFACC15),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.settings, size: 18),
                const SizedBox(width: 8),
                const Text('Manage Subscription Plans'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to subscription management screen
  void _navigateToSubscriptionManagement(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SubscriptionPlanManagementScreen(),
      ),
    );
  }

  /// Build section with title and optional add button
  Widget _buildSection({
    required String title,
    required bool isEmpty,
    required String emptyMessage,
    required Widget child,
    VoidCallback? onAdd,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: ATextStyle.title.copyWith(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (onAdd != null)
              IconButton(
                onPressed: onAdd,
                icon: const Icon(Icons.add, color: Color(0xFFFACC15)),
                style: IconButton.styleFrom(
                  backgroundColor: const Color(0xFFFACC15).withOpacity(0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (isEmpty)
          _buildEmptyState(emptyMessage)
        else
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF1F2937),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF374151)),
            ),
            child: child,
          ),
      ],
    );
  }

  /// Build empty state widget
  Widget _buildEmptyState(String message) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151), style: BorderStyle.solid),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.grey[400],
            size: 32,
          ),
          const SizedBox(height: 12),
          Text(
            message,
            style: ATextStyle.medium.copyWith(
              color: Colors.grey[400],
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Format time ago
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} month${(difference.inDays / 30).floor() > 1 ? 's' : ''} ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    }
  }
}
