import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import '../../domain/market_models.dart';
import '../../application/market_provider.dart';
import '../workout_plan_draft_list_page.dart';
import '../nutrition_templates_page.dart';


class InstructorExploreTab extends HookConsumerWidget {
  const InstructorExploreTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                'Explore'.hardcoded,
                style: ATextStyle.title.copyWith(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // Plan Drafts Section
              Text(
                'Plan Drafts',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildPlanCard(
                      title: 'Workout Plan Draft',
                      icon: Icons.fitness_center,
                      color: const Color(0xFF3B82F6),
                      onTap: () => _navigateToWorkoutDraft(context),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildPlanCard(
                      title: 'Nutrition Plan Draft',
                      icon: Icons.restaurant,
                      color: const Color(0xFF10B981),
                      onTap: () => _navigateToNutritionDraft(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Explore Section
              Text(
                'Explore',
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildExploreCard(
                      title: 'Explore Trainers',
                      icon: Icons.people,
                      color: const Color(0xFF8B5CF6),
                      onTap: () => _navigateToTrainers(context),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildExploreCard(
                      title: 'Settings',
                      icon: Icons.settings,
                      color: const Color(0xFF6B7280),
                      onTap: () => _navigateToSettings(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // FitGo Market Section
              _buildMarketSection(context, ref),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlanCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: ATextStyle.small.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildExploreCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF374151)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: ATextStyle.small.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMarketSection(BuildContext context, WidgetRef ref) {
    final featuredProductsAsync = ref.watch(featuredProductsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // FitGo Market Header
        Text(
          'FitGo Market',
          style: ATextStyle.medium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        // Market Card Container
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF1F2937),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFF374151)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.store,
                    color: const Color(0xFFFACC15),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Nutrition & Supplements',
                    style: ATextStyle.small.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => _navigateToFullMarket(context),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      minimumSize: Size.zero,
                    ),
                    child: Text(
                      'View All',
                      style: ATextStyle.small.copyWith(
                        color: const Color(0xFFFACC15),
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Products horizontal scroll
              featuredProductsAsync.when(
                loading: () => _buildMarketLoadingState(),
                error: (error, stack) => _buildMarketErrorState(error),
                data: (products) => _buildMarketProductsList(products),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMarketLoadingState() {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        itemBuilder: (context, index) {
          return Container(
            width: 100,
            margin: EdgeInsets.only(right: index < 2 ? 8 : 0),
            decoration: BoxDecoration(
              color: const Color(0xFF374151),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: Color(0xFFFACC15),
                strokeWidth: 2,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMarketErrorState(Object error) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red[400],
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Failed to load products',
              style: ATextStyle.small.copyWith(color: Colors.red[400]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarketProductsList(List<MarketProduct> products) {
    if (products.isEmpty) {
      return Container(
        height: 80,
        decoration: BoxDecoration(
          color: const Color(0xFF374151),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            'No products available',
            style: ATextStyle.small.copyWith(color: Colors.grey),
          ),
        ),
      );
    }

    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return Container(
            width: 100,
            margin: EdgeInsets.only(right: index < products.length - 1 ? 8 : 0),
            child: _buildCompactProductCard(product),
          );
        },
      ),
    );
  }

  Widget _buildCompactProductCard(MarketProduct product) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF374151),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToProductDetail(product),
          borderRadius: BorderRadius.circular(8),
          child: Column(
            children: [
              // Product Icon
              Expanded(
                flex: 2,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: product.category.color.withValues(alpha: 0.1),
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          product.category.icon,
                          color: product.category.color,
                          size: 24,
                        ),
                      ),
                      if (product.isPopular)
                        Positioned(
                          top: 4,
                          right: 4,
                          child: Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: const Color(0xFFFACC15),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Center(
                              child: Text(
                                '🔥',
                                style: TextStyle(fontSize: 8),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // Product Info
              Expanded(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        product.name,
                        style: ATextStyle.small.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          fontSize: 10,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        product.formattedPrice,
                        style: ATextStyle.small.copyWith(
                          color: const Color(0xFFFACC15),
                          fontWeight: FontWeight.bold,
                          fontSize: 9,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Navigation methods
  void _navigateToWorkoutDraft(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const WorkoutPlanDraftListPage(),
      ),
    );
  }



  void _navigateToFullMarket(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Full Market View - Coming Soon!'),
        backgroundColor: Color(0xFFFACC15),
      ),
    );
  }

  void _navigateToProductDetail(MarketProduct product) {
    // TODO: Navigate to product detail page
  }

  void _navigateToTrainers(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Trainers Network - Coming Soon!'),
        backgroundColor: Color(0xFF8B5CF6),
      ),
    );
  }

  void _navigateToSettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings - Coming Soon!'),
        backgroundColor: Color(0xFF6B7280),
      ),
    );
  }

  void _navigateToNutritionDraft(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NutritionTemplatesPage(),
      ),
    );
  }
}
