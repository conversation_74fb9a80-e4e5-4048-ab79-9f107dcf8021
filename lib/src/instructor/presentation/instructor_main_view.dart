import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import '../application/instructor_provider.dart';
import 'components/instructor_homepage.dart';
import 'components/instructor_members_tab.dart';
import 'components/instructor_profile_tab.dart';
import 'components/instructor_explore_tab.dart';

class InstructorMainView extends HookConsumerWidget {
  const InstructorMainView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTab = ref.watch(instructorBottomNavTabProvider);

    // Initialize dashboard when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(instructorDashboardNotifierProvider.notifier).initializeDashboard();
      });
      return null;
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: _buildCurrentTabContent(currentTab),
      bottomNavigationBar: _buildBottomNavigationBar(ref, currentTab),
    );
  }

  Widget _buildCurrentTabContent(int currentTab) {
    switch (currentTab) {
      case 0:
        return const InstructorHomepage();
      case 1:
        return const InstructorMembersTab();
      case 2:
        return const InstructorProfileTab();
      case 3:
        return const InstructorExploreTab();
      default:
        return const InstructorHomepage();
    }
  }

  Widget _buildBottomNavigationBar(WidgetRef ref, int currentTab) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF1F2937),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(ref, 0, Icons.home, 'Home', currentTab),
              _buildNavItem(ref, 1, Icons.people, 'Members', currentTab),
              _buildNavItem(ref, 2, Icons.person, 'Profile', currentTab),
              _buildNavItem(ref, 3, Icons.explore, 'Explore', currentTab),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(WidgetRef ref, int index, IconData icon, String label, int currentTab) {
    final isSelected = currentTab == index;

    return GestureDetector(
      onTap: () => ref.read(instructorBottomNavTabProvider.notifier).state = index,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFACC15).withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? const Color(0xFFFACC15) : Colors.grey,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? const Color(0xFFFACC15) : Colors.grey,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Home Tab for Instructors
class InstructorHomeTab extends StatelessWidget {
  const InstructorHomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Dashboard'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildDashboardCard(
                  'My Courses'.hardcoded,
                  '12',
                  Icons.fitness_center,
                  AColor.buttonColor,
                ),
                _buildDashboardCard(
                  'Students'.hardcoded,
                  '45',
                  Icons.people,
                  Colors.green,
                ),
                _buildDashboardCard(
                  'Revenue'.hardcoded,
                  '\$2,450',
                  Icons.attach_money,
                  Colors.orange,
                ),
                _buildDashboardCard(
                  'Reviews'.hardcoded,
                  '4.8',
                  Icons.star,
                  Colors.yellow,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AColor.bottomSheetBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AColor.textSecondaryColor),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          TextWidget(
            value,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 4),
          TextWidget(
            title,
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
        ],
      ),
    );
  }
}






