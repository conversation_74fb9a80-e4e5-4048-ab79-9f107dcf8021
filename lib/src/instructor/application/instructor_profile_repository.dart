import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import '../domain/instructor_profile_models.dart';

/// Provider for instructor profile repository
final instructorProfileRepositoryProvider = Provider<InstructorProfileRepository>((ref) {
  return InstructorProfileRepository();
});

/// Repository for instructor profile data operations
class InstructorProfileRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get complete instructor profile data
  Future<InstructorProfileData> getInstructorProfile(String instructorId) async {
    try {
      // Get basic info
      final basicInfo = await _getBasicInfo(instructorId);
      
      // Get work history
      final workHistory = await _getWorkHistory(instructorId);
      
      // Get certifications
      final certifications = await _getCertifications(instructorId);
      
      // Get reviews
      final reviews = await _getReviews(instructorId);
      
      // Get FAQs
      final faqs = await _getFAQs(instructorId);
      
      // Get subscription info
      final subscriptionInfo = await _getSubscriptionInfo(instructorId);

      return InstructorProfileData(
        basicInfo: basicInfo,
        workHistory: workHistory,
        certifications: certifications,
        reviews: reviews,
        faqs: faqs,
        subscriptionInfo: subscriptionInfo,
      );
    } catch (e) {
      debugPrint('❌ Error getting instructor profile: $e');
      rethrow;
    }
  }

  /// Get basic instructor information
  Future<InstructorBasicInfo> _getBasicInfo(String instructorId) async {
    try {
      final response = await _supabase
          .from('instructors')
          .select('id, name, surname, title, photo_url, experience_years')
          .eq('id', instructorId)
          .single();

      // Get reviews count and average rating
      final reviewsResponse = await _supabase
          .from('trainer_reviews')
          .select('rating')
          .eq('trainer_id', instructorId);

      double averageRating = 0.0;
      int totalReviews = reviewsResponse.length;
      if (totalReviews > 0) {
        final ratings = reviewsResponse.map((r) => r['rating'] as num).toList();
        averageRating = ratings.reduce((a, b) => a + b) / ratings.length;
      }

      // Get student count through enrollments
      final studentCountResponse = await _supabase
          .from('user_enrollments')
          .select('user_id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      final totalStudents = studentCountResponse.length;

      // Combine name and surname
      final name = response['name'] as String? ?? '';
      final surname = response['surname'] as String? ?? '';
      final fullName = '$name $surname'.trim();

      return InstructorBasicInfo(
        id: instructorId,
        name: fullName.isNotEmpty ? fullName : 'Enter your name',
        title: response['title'] as String? ?? 'Enter your title',
        profilePictureUrl: response['photo_url'] as String?,
        averageRating: averageRating,
        totalReviews: totalReviews,
        experienceYears: response['experience_years'] as int? ?? 0,
        totalStudents: totalStudents,
        primaryCertification: 'Add certification', // Will be set from certifications
        canEdit: true,
      );
    } catch (e) {
      debugPrint('❌ Error getting basic info: $e');
      rethrow;
    }
  }

  /// Get work history
  Future<List<WorkExperience>> _getWorkHistory(String instructorId) async {
    try {
      final response = await _supabase
          .from('trainer_work_history')
          .select('id, gym, role, duration')
          .eq('trainer_id', instructorId)
          .order('id', ascending: false);

      return response.map<WorkExperience>((work) {
        final duration = work['duration'] as String? ?? '';
        final parts = duration.split(' - ');
        final startYear = parts.isNotEmpty ? parts[0] : '';
        final endYear = parts.length > 1 && parts[1] != 'Present' ? parts[1] : null;
        final isCurrent = parts.length > 1 && parts[1] == 'Present';

        return WorkExperience(
          id: work['id'].toString(),
          companyName: work['gym'] as String? ?? 'Unknown Company',
          role: work['role'] as String? ?? 'Unknown Role',
          startYear: startYear,
          endYear: endYear,
          description: '', // Not stored in current schema
          isCurrent: isCurrent,
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting work history: $e');
      return []; // Return empty list if no work history
    }
  }

  /// Get certifications
  Future<List<Certification>> _getCertifications(String instructorId) async {
    try {
      final response = await _supabase
          .from('trainer_certifications')
          .select('id, title, organization')
          .eq('trainer_id', instructorId)
          .order('id', ascending: false);

      return response.map<Certification>((cert) {
        return Certification(
          id: cert['id'].toString(),
          name: cert['title'] as String? ?? 'Unknown Certification',
          issuer: cert['organization'] as String? ?? 'Unknown Organization',
          year: '', // Not stored in current schema
          isVerified: false, // Not stored in current schema
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting certifications: $e');
      return []; // Return empty list if no certifications
    }
  }

  /// Get client reviews
  Future<List<ClientReview>> _getReviews(String instructorId) async {
    try {
      final response = await _supabase
          .from('trainer_reviews')
          .select('id, trainer_id, client_name, avatar_url, rating, comment, created_at')
          .eq('trainer_id', instructorId)
          .order('created_at', ascending: false)
          .limit(10);

      return response.map<ClientReview>((review) {
        return ClientReview(
          id: review['id'].toString(),
          clientName: review['client_name'] as String? ?? 'Anonymous',
          clientAvatarUrl: review['avatar_url'] as String?,
          rating: (review['rating'] as num).toDouble(),
          comment: review['comment'] as String? ?? '',
          createdAt: DateTime.parse(review['created_at'] as String),
          canModerate: true,
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting reviews: $e');
      return []; // Return empty list if no reviews
    }
  }

  /// Get FAQs
  Future<List<FAQ>> _getFAQs(String instructorId) async {
    // FAQ table doesn't exist yet, return empty list
    debugPrint('⚠️ FAQ table does not exist yet - returning empty list');
    return [];
  }

  /// Get subscription information
  Future<SubscriptionInfo?> _getSubscriptionInfo(String instructorId) async {
    // This would query subscription tables
    // For now, return mock data
    return const SubscriptionInfo(
      planName: 'Professional Trainer',
      status: 'active',
      expiresAt: null, // Ongoing subscription
      autoRenew: true,
      monthlyPrice: 49.99,
    );
  }



  /// Update basic info
  Future<void> updateBasicInfo(String instructorId, InstructorBasicInfo basicInfo) async {
    try {
      debugPrint('🔄 Updating basic info for instructor: $instructorId');
      debugPrint('📝 Data: ${basicInfo.name}, ${basicInfo.title}');

      // Split name into name and surname if needed
      final nameParts = basicInfo.name.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts[0] : basicInfo.name;
      final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      // Update without title for now due to schema cache issue
      await _supabase
          .from('instructors')
          .update({
            'name': firstName,
            'surname': lastName.isNotEmpty ? lastName : null,
            'photo_url': basicInfo.profilePictureUrl,
            'experience_years': basicInfo.experienceYears,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', instructorId);

      // Try to update title separately
      try {
        await _supabase
            .from('instructors')
            .update({'title': basicInfo.title})
            .eq('id', instructorId);
        debugPrint('✅ Title updated successfully');
      } catch (titleError) {
        debugPrint('⚠️ Could not update title: $titleError');
      }

      debugPrint('✅ Basic info updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating basic info: $e');
      rethrow;
    }
  }

  /// Add work experience
  Future<void> addWorkExperience(String instructorId, WorkExperience experience) async {
    try {
      debugPrint('🔄 Adding work experience for instructor: $instructorId');
      debugPrint('📝 Company: ${experience.companyName}, Role: ${experience.role}');

      // Format duration from start/end years
      String duration = experience.startYear;
      if (experience.isCurrent) {
        duration += ' - Present';
      } else if (experience.endYear != null && experience.endYear!.isNotEmpty) {
        duration += ' - ${experience.endYear}';
      }

      await _supabase
          .from('trainer_work_history')
          .insert({
            'trainer_id': instructorId,
            'gym': experience.companyName,
            'role': experience.role,
            'duration': duration,
            'created_at': DateTime.now().toIso8601String(),
          });

      debugPrint('✅ Work experience added successfully');
    } catch (e) {
      debugPrint('❌ Error adding work experience: $e');
      rethrow;
    }
  }

  /// Update work experience
  Future<void> updateWorkExperience(String instructorId, WorkExperience experience) async {
    try {
      debugPrint('🔄 Updating work experience: ${experience.id}');

      // Format duration from start/end years
      String duration = experience.startYear;
      if (experience.isCurrent) {
        duration += ' - Present';
      } else if (experience.endYear != null && experience.endYear!.isNotEmpty) {
        duration += ' - ${experience.endYear}';
      }

      await _supabase
          .from('trainer_work_history')
          .update({
            'gym': experience.companyName,
            'role': experience.role,
            'duration': duration,
          })
          .eq('id', int.parse(experience.id));

      debugPrint('✅ Work experience updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating work experience: $e');
      rethrow;
    }
  }

  /// Delete work experience
  Future<void> deleteWorkExperience(String workExperienceId) async {
    try {
      debugPrint('🔄 Deleting work experience: $workExperienceId');

      await _supabase
          .from('trainer_work_history')
          .delete()
          .eq('id', int.parse(workExperienceId));

      debugPrint('✅ Work experience deleted successfully');
    } catch (e) {
      debugPrint('❌ Error deleting work experience: $e');
      rethrow;
    }
  }

  /// Add certification
  Future<void> addCertification(String instructorId, Certification certification) async {
    try {
      debugPrint('🔄 Adding certification for instructor: $instructorId');
      debugPrint('📝 Certification: ${certification.name} from ${certification.issuer}');

      await _supabase
          .from('trainer_certifications')
          .insert({
            'trainer_id': instructorId,
            'title': certification.name,
            'organization': certification.issuer,
            'created_at': DateTime.now().toIso8601String(),
          });

      debugPrint('✅ Certification added successfully');
    } catch (e) {
      debugPrint('❌ Error adding certification: $e');
      rethrow;
    }
  }

  /// Update certification
  Future<void> updateCertification(String instructorId, Certification certification) async {
    try {
      debugPrint('🔄 Updating certification: ${certification.id}');

      await _supabase
          .from('trainer_certifications')
          .update({
            'title': certification.name,
            'organization': certification.issuer,
          })
          .eq('id', int.parse(certification.id));

      debugPrint('✅ Certification updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating certification: $e');
      rethrow;
    }
  }

  /// Delete certification
  Future<void> deleteCertification(String certificationId) async {
    try {
      debugPrint('🔄 Deleting certification: $certificationId');

      await _supabase
          .from('trainer_certifications')
          .delete()
          .eq('id', int.parse(certificationId));

      debugPrint('✅ Certification deleted successfully');
    } catch (e) {
      debugPrint('❌ Error deleting certification: $e');
      rethrow;
    }
  }

  /// Add FAQ
  Future<void> addFAQ(String instructorId, FAQ faq) async {
    try {
      debugPrint('🔄 Adding FAQ for instructor: $instructorId');
      debugPrint('📝 Question: ${faq.question}');
      debugPrint('⚠️ FAQ table does not exist yet - feature disabled');

      // TODO: Create instructor_faqs table first
      // await _supabase
      //     .from('instructor_faqs')
      //     .insert({
      //       'instructor_id': instructorId,
      //       'question': faq.question,
      //       'answer': faq.answer,
      //       'order_index': faq.order,
      //       'created_at': DateTime.now().toIso8601String(),
      //     });

      debugPrint('✅ FAQ feature temporarily disabled');
    } catch (e) {
      debugPrint('❌ Error adding FAQ: $e');
      // Don't throw error for now
    }
  }

  /// Update FAQ
  Future<void> updateFAQ(String instructorId, FAQ faq) async {
    debugPrint('⚠️ FAQ update feature temporarily disabled - table does not exist');
    // TODO: Implement after creating instructor_faqs table
  }

  /// Delete FAQ
  Future<void> deleteFAQ(String faqId) async {
    debugPrint('⚠️ FAQ delete feature temporarily disabled - table does not exist');
    // TODO: Implement after creating instructor_faqs table
  }

  /// Delete review (moderation)
  Future<void> deleteReview(String reviewId) async {
    await _supabase
        .from('trainer_reviews')
        .delete()
        .eq('id', reviewId);
  }
}
