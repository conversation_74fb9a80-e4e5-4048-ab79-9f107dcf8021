import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import '../domain/subscription_plan_models.dart';

/// Provider for subscription plan repository
final subscriptionPlanRepositoryProvider = Provider<SubscriptionPlanRepository>((ref) {
  return SubscriptionPlanRepository();
});

/// Repository for instructor subscription plan management
class SubscriptionPlanRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get instructor's subscription configuration
  Future<InstructorSubscriptionConfig> getInstructorSubscriptionConfig(String instructorId) async {
    try {
      // Get base configuration
      final configResponse = await _supabase
          .from('instructor_subscription_configs')
          .select('*')
          .eq('instructor_id', instructorId)
          .maybeSingle();

      // Get discount codes
      final discountCodesResponse = await _supabase
          .from('instructor_discount_codes')
          .select('*')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      if (configResponse == null) {
        // Return default configuration for new instructors
        return _getDefaultConfiguration();
      }

      return _parseConfigurationFromDatabase(configResponse, discountCodesResponse);
    } catch (e) {
      // Return mock data for development
      return _getMockConfiguration();
    }
  }

  /// Save instructor's subscription configuration
  Future<void> saveInstructorSubscriptionConfig(
    String instructorId,
    InstructorSubscriptionConfig config,
  ) async {
    try {
      // Save main configuration
      await _supabase
          .from('instructor_subscription_configs')
          .upsert({
            'instructor_id': instructorId,
            'base_monthly_price': config.baseMonthlyPrice,
            'basic_plan_pricing': _serializePlanPricing(config.basicPlanPricing),
            'premium_plan_pricing': _serializePlanPricing(config.premiumPlanPricing),
            'submission_status': config.submissionStatus.name,
            'last_submitted_at': config.lastSubmittedAt?.toIso8601String(),
            'admin_feedback': config.adminFeedback,
            'updated_at': DateTime.now().toIso8601String(),
          });

      // Save discount codes
      for (final discountCode in config.discountCodes) {
        await _saveDiscountCode(instructorId, discountCode);
      }
    } catch (e) {
      throw Exception('Failed to save subscription configuration: $e');
    }
  }

  /// Submit configuration for admin review
  Future<void> submitForReview(String instructorId, InstructorSubscriptionConfig config) async {
    try {
      final updatedConfig = config.copyWith(
        submissionStatus: SubmissionStatus.submitted,
        lastSubmittedAt: DateTime.now(),
      );

      await saveInstructorSubscriptionConfig(instructorId, updatedConfig);

      // Update instructor status to be publicly visible
      await _updateInstructorPublicStatus(instructorId, true);

      // Notify admin (could be implemented with notifications table)
      await _notifyAdminOfSubmission(instructorId);
    } catch (e) {
      throw Exception('Failed to submit for review: $e');
    }
  }

  /// Create a new discount code
  Future<DiscountCode> createDiscountCode(String instructorId, DiscountCode discountCode) async {
    try {
      final response = await _supabase
          .from('instructor_discount_codes')
          .insert({
            'instructor_id': instructorId,
            'code': discountCode.code,
            'discount_percentage': discountCode.discountPercentage,
            'expiry_date': discountCode.expiryDate.toIso8601String(),
            'is_active': discountCode.isActive,
            'usage_limit': discountCode.usageLimit,
            'usage_count': discountCode.usageCount,
            'applicable_plans': discountCode.applicablePlans.map((p) => p.name).toList(),
            'created_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();

      return DiscountCode(
        id: response['id'].toString(),
        code: response['code'] as String,
        discountPercentage: (response['discount_percentage'] as num).toDouble(),
        expiryDate: DateTime.parse(response['expiry_date'] as String),
        isActive: response['is_active'] as bool,
        usageLimit: response['usage_limit'] as int,
        usageCount: response['usage_count'] as int,
        applicablePlans: (response['applicable_plans'] as List)
            .map((p) => PlanType.values.firstWhere((type) => type.name == p))
            .toList(),
      );
    } catch (e) {
      throw Exception('Failed to create discount code: $e');
    }
  }

  /// Delete a discount code
  Future<void> deleteDiscountCode(String discountCodeId) async {
    try {
      await _supabase
          .from('instructor_discount_codes')
          .update({'is_active': false})
          .eq('id', discountCodeId);
    } catch (e) {
      throw Exception('Failed to delete discount code: $e');
    }
  }

  /// Validate subscription configuration
  ValidationResult validateConfiguration(InstructorSubscriptionConfig config) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate base price
    if (config.baseMonthlyPrice <= 0) {
      errors.add('Base monthly price must be greater than 0');
    }
    if (config.baseMonthlyPrice < 10) {
      warnings.add('Base monthly price seems low. Consider pricing competitively.');
    }
    if (config.baseMonthlyPrice > 200) {
      warnings.add('Base monthly price seems high. This might limit your client base.');
    }

    // Validate plan pricing
    for (final duration in PlanDuration.values) {
      final basicPricing = config.basicPlanPricing[duration];
      final premiumPricing = config.premiumPlanPricing[duration];

      if (basicPricing == null) {
        errors.add('Basic plan pricing for ${duration.displayName} is missing');
      }
      if (premiumPricing == null) {
        errors.add('Premium plan pricing for ${duration.displayName} is missing');
      }

      if (basicPricing != null && premiumPricing != null) {
        if (premiumPricing.monthlyPrice <= basicPricing.monthlyPrice) {
          errors.add('Premium plan must be more expensive than Basic plan for ${duration.displayName}');
        }
      }
    }

    // Validate discount codes
    for (final discountCode in config.discountCodes) {
      if (discountCode.code.length < 3) {
        errors.add('Discount code "${discountCode.code}" is too short');
      }
      if (discountCode.discountPercentage <= 0 || discountCode.discountPercentage > 50) {
        errors.add('Discount percentage for "${discountCode.code}" must be between 1% and 50%');
      }
      if (discountCode.expiryDate.isBefore(DateTime.now())) {
        warnings.add('Discount code "${discountCode.code}" has already expired');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Calculate pricing for a plan based on base price and duration
  PlanPricing calculatePlanPricing({
    required double basePrice,
    required PlanDuration duration,
    required PlanType planType,
    double? customMultiplier,
  }) {
    final multiplier = customMultiplier ?? (planType == PlanType.premium ? 1.5 : 1.0);
    final monthlyPrice = basePrice * multiplier;
    final discountedMonthlyPrice = monthlyPrice * duration.discountMultiplier;
    final totalPrice = discountedMonthlyPrice * duration.months;

    return PlanPricing(
      monthlyPrice: discountedMonthlyPrice,
      totalPrice: totalPrice,
      duration: duration,
      features: PlanFeatures.getFeaturesForPlan(planType),
      isCustomPrice: customMultiplier != null,
    );
  }

  /// Private helper methods
  InstructorSubscriptionConfig _getDefaultConfiguration() {
    const basePrice = 49.99;
    
    final basicPricing = <PlanDuration, PlanPricing>{};
    final premiumPricing = <PlanDuration, PlanPricing>{};

    for (final duration in PlanDuration.values) {
      basicPricing[duration] = calculatePlanPricing(
        basePrice: basePrice,
        duration: duration,
        planType: PlanType.basic,
      );
      
      premiumPricing[duration] = calculatePlanPricing(
        basePrice: basePrice,
        duration: duration,
        planType: PlanType.premium,
      );
    }

    return InstructorSubscriptionConfig(
      baseMonthlyPrice: basePrice,
      basicPlanPricing: basicPricing,
      premiumPlanPricing: premiumPricing,
      discountCodes: [],
    );
  }

  InstructorSubscriptionConfig _getMockConfiguration() {
    const basePrice = 49.99;
    
    final basicPricing = <PlanDuration, PlanPricing>{};
    final premiumPricing = <PlanDuration, PlanPricing>{};

    for (final duration in PlanDuration.values) {
      basicPricing[duration] = calculatePlanPricing(
        basePrice: basePrice,
        duration: duration,
        planType: PlanType.basic,
      );
      
      premiumPricing[duration] = calculatePlanPricing(
        basePrice: basePrice,
        duration: duration,
        planType: PlanType.premium,
      );
    }

    return InstructorSubscriptionConfig(
      baseMonthlyPrice: basePrice,
      basicPlanPricing: basicPricing,
      premiumPlanPricing: premiumPricing,
      discountCodes: [
        DiscountCode(
          id: '1',
          code: 'SUMMER2024',
          discountPercentage: 20.0,
          expiryDate: DateTime.now().add(const Duration(days: 90)),
          usageCount: 5,
          usageLimit: 100,
        ),
        DiscountCode(
          id: '2',
          code: 'NEWCLIENT',
          discountPercentage: 15.0,
          expiryDate: DateTime.now().add(const Duration(days: 365)),
          usageCount: 12,
          usageLimit: 50,
        ),
      ],
      submissionStatus: SubmissionStatus.draft,
    );
  }

  InstructorSubscriptionConfig _parseConfigurationFromDatabase(
    Map<String, dynamic> configData,
    List<Map<String, dynamic>> discountCodesData,
  ) {
    // Implementation for parsing database data
    // This would convert the stored JSON back to objects
    return _getMockConfiguration(); // Placeholder
  }

  Map<String, dynamic> _serializePlanPricing(Map<PlanDuration, PlanPricing> pricing) {
    // Implementation for serializing plan pricing to JSON
    return {}; // Placeholder
  }

  Future<void> _saveDiscountCode(String instructorId, DiscountCode discountCode) async {
    // Implementation for saving individual discount codes
  }

  /// Update instructor public visibility status
  Future<void> _updateInstructorPublicStatus(String instructorId, bool isPublic) async {
    try {
      await _supabase
          .from('instructors')
          .update({
            'is_public': isPublic,
            'is_available': isPublic,
            'profile_completed': isPublic,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', instructorId);
    } catch (e) {
      throw Exception('Failed to update instructor public status: $e');
    }
  }

  Future<void> _notifyAdminOfSubmission(String instructorId) async {
    try {
      // Create admin notification
      await _supabase
          .from('admin_notifications')
          .insert({
            'type': 'instructor_submission',
            'instructor_id': instructorId,
            'message': 'New instructor submitted profile for review',
            'is_read': false,
            'created_at': DateTime.now().toIso8601String(),
          });
    } catch (e) {
      // Notification failure shouldn't block the submission
      debugPrint('Failed to notify admin: $e');
    }
  }
}
