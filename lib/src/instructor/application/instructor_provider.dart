import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import '../domain/instructor_models.dart';
import 'instructor_repository.dart';
import 'instructor_navigation_service.dart';

/// Provider for current instructor ID
final currentInstructorIdProvider = StateProvider<String?>((ref) {
  final user = Supabase.instance.client.auth.currentUser;
  return user?.id;
});

/// Provider for instructor dashboard data
final instructorDashboardProvider = FutureProvider<InstructorDashboard>((ref) async {
  final instructorId = ref.watch(currentInstructorIdProvider);
  if (instructorId == null) {
    throw Exception('No instructor ID available');
  }

  final repository = ref.read(instructorRepositoryProvider);
  return repository.getInstructorDashboard(instructorId);
});

/// Provider for instructor dashboard state management
final instructorDashboardNotifierProvider = 
    StateNotifierProvider<InstructorDashboardNotifier, InstructorDashboardState>((ref) {
  return InstructorDashboardNotifier(ref);
});

/// State class for instructor dashboard
class InstructorDashboardState {
  final InstructorDashboard? dashboard;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const InstructorDashboardState({
    this.dashboard,
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  InstructorDashboardState copyWith({
    InstructorDashboard? dashboard,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return InstructorDashboardState(
      dashboard: dashboard ?? this.dashboard,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// State notifier for instructor dashboard
class InstructorDashboardNotifier extends StateNotifier<InstructorDashboardState> {
  final Ref _ref;

  InstructorDashboardNotifier(this._ref) : super(const InstructorDashboardState());

  /// Initialize dashboard data
  Future<void> initializeDashboard() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      final repository = _ref.read(instructorRepositoryProvider);
      final dashboard = await repository.getInstructorDashboard(instructorId);

      state = state.copyWith(
        dashboard: dashboard,
        isLoading: false,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Refresh dashboard data
  Future<void> refreshDashboard() async {
    await initializeDashboard();
  }

  /// Handle priority alert action
  Future<void> handleAlertAction(String alertId, String action) async {
    try {
      final repository = _ref.read(instructorRepositoryProvider);
      await repository.handleAlertAction(alertId, action);
      
      // Refresh dashboard to update alerts
      await refreshDashboard();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Handle task tap
  void handleTaskTap(TaskType taskType, BuildContext context) {
    final navigationService = _ref.read(instructorNavigationServiceProvider);
    navigationService.navigateToTaskView(context, taskType.displayName);
  }

  /// Handle new student view
  void handleNewStudentView(String studentId, BuildContext context) {
    final navigationService = _ref.read(instructorNavigationServiceProvider);
    navigationService.navigateToStudentProfile(context, studentId);
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for checking if current user is instructor
final isCurrentUserInstructorProvider = FutureProvider<bool>((ref) async {
  try {
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) return false;

    // Check user role from profiles table
    final response = await Supabase.instance.client
        .from('profiles')
        .select('role')
        .eq('auth_user_id', user.id)
        .maybeSingle();

    if (response != null) {
      return response['role'] == 'instructor';
    }

    // Fallback: check instructors table
    final instructorResponse = await Supabase.instance.client
        .from('instructors')
        .select('id')
        .eq('id', user.id)
        .maybeSingle();

    return instructorResponse != null;
  } catch (e) {
    return false;
  }
});

/// Provider for instructor profile
final instructorProfileProvider = FutureProvider<InstructorProfile?>((ref) async {
  final instructorId = ref.watch(currentInstructorIdProvider);
  if (instructorId == null) return null;

  try {
    final repository = ref.read(instructorRepositoryProvider);
    final dashboard = await repository.getInstructorDashboard(instructorId);
    return dashboard.profile;
  } catch (e) {
    return null;
  }
});

/// Provider for instructor statistics
final instructorStatsProvider = FutureProvider<InstructorStats?>((ref) async {
  final dashboard = await ref.watch(instructorDashboardProvider.future);
  return dashboard.stats;
});

/// Provider for priority alerts
final priorityAlertsProvider = FutureProvider<List<PriorityAlert>>((ref) async {
  final dashboard = await ref.watch(instructorDashboardProvider.future);
  return dashboard.priorityAlerts;
});

/// Provider for task overview
final taskOverviewProvider = FutureProvider<TaskOverview?>((ref) async {
  final dashboard = await ref.watch(instructorDashboardProvider.future);
  return dashboard.taskOverview;
});

/// Provider for new students
final newStudentsProvider = FutureProvider<List<NewStudent>>((ref) async {
  final dashboard = await ref.watch(instructorDashboardProvider.future);
  return dashboard.newStudents;
});

/// Provider for weekly progress
final weeklyProgressProvider = FutureProvider<WeeklyProgress?>((ref) async {
  final dashboard = await ref.watch(instructorDashboardProvider.future);
  return dashboard.weeklyProgress;
});

/// Provider for bottom navigation tab state
final instructorBottomNavTabProvider = StateProvider<int>((ref) => 0);

/// Enum for instructor bottom navigation tabs
enum InstructorBottomNavTab {
  home,
  members,
  profile,
  explore;

  String get label {
    switch (this) {
      case InstructorBottomNavTab.home:
        return 'Home';
      case InstructorBottomNavTab.members:
        return 'Members';
      case InstructorBottomNavTab.profile:
        return 'Profile';
      case InstructorBottomNavTab.explore:
        return 'Explore';
    }
  }

  String get icon {
    switch (this) {
      case InstructorBottomNavTab.home:
        return '🏠';
      case InstructorBottomNavTab.members:
        return '👥';
      case InstructorBottomNavTab.profile:
        return '👤';
      case InstructorBottomNavTab.explore:
        return '🔍';
    }
  }
}
