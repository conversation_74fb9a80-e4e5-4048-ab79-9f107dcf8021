import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/student_models.dart';

/// Provider for students repository
final studentsRepositoryProvider = Provider<StudentsRepository>((ref) {
  return StudentsRepository();
});

/// Repository for instructor's student management
class StudentsRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get all students assigned to an instructor
  Future<List<InstructorStudent>> getInstructorStudents(
    String instructorId, {
    String? searchQuery,
    StudentFilter filter = StudentFilter.all,
    StudentSortOption sortOption = StudentSortOption.activityDesc,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      // Debug: Print instructor ID
      debugPrint('🔍 Getting students for instructor ID: $instructorId');

      // Get enrollments for this instructor
      final enrollmentsQuery = await _supabase
          .from('user_enrollments')
          .select('user_id, enrolled_at, is_active')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      debugPrint('✅ Found ${enrollmentsQuery.length} enrollments in user_enrollments');

      List<dynamic> enrollmentsResponse = [];

      if (enrollmentsQuery.isNotEmpty) {
        // Get user IDs from enrollments
        final userIds = enrollmentsQuery
            .map((e) => e['user_id'] as String)
            .toList();

        // Get profiles for these users
        debugPrint('🔍 Looking for profiles with IDs: $userIds');
        final profilesResponse = await _supabase
            .from('profiles')
            .select('id, name, surname, email, avatar_url, updated_at')
            .inFilter('id', userIds);

        debugPrint('🔍 Profiles response: $profilesResponse');

        // Combine enrollment and profile data
        enrollmentsResponse = enrollmentsQuery.map((enrollment) {
          final profile = profilesResponse.firstWhere(
            (p) => p['id'] == enrollment['user_id'],
            orElse: () => {},
          );
          return {
            'user_id': enrollment['user_id'],
            'enrolled_at': enrollment['enrolled_at'],
            'profiles': profile,
          };
        }).toList();

        debugPrint('✅ Combined ${enrollmentsResponse.length} student records with profiles');
      }

      final students = <InstructorStudent>[];

      for (final enrollment in enrollmentsResponse) {
        final profile = enrollment['profiles'];
        debugPrint('🔍 Processing enrollment: ${enrollment.toString()}');
        debugPrint('🔍 Profile data: ${profile.toString()}');

        if (profile == null || (profile is Map && profile.isEmpty)) {
          debugPrint('❌ Skipping enrollment due to null/empty profile');
          continue;
        }

        final userId = enrollment['user_id'] as String;
        final enrolledAt = enrollment['enrolled_at'];

        debugPrint('👤 Processing student: $userId');

        // Get student's plan assignment status
        final statusCounts = await _getStudentPlanStatus(userId, instructorId);

        // Calculate activity score
        final activityScore = await _calculateActivityScore(userId);

        // Handle profile names
        final firstName = profile['name'] as String? ?? '';
        final lastName = profile['surname'] as String? ?? '';
        final fullName = '$firstName $lastName'.trim();

        final student = InstructorStudent(
          id: userId,
          name: fullName.isNotEmpty ? fullName :
                (profile['email'] as String? ?? 'Unknown').split('@')[0],
          email: profile['email'] as String? ?? '',
          profileImageUrl: profile['avatar_url'] as String?,
          joinedAt: enrolledAt != null ? DateTime.parse(enrolledAt as String) : DateTime.now(),
          isOnline: _isUserOnline(profile['updated_at'] as String?),
          planType: StudentPlanType.basic,
          statusCounts: statusCounts,
          lastActiveAt: profile['updated_at'] != null
              ? DateTime.parse(profile['updated_at'] as String)
              : null,
          activityScore: activityScore,
        );

        students.add(student);
      }

      debugPrint('🎯 Total students processed: ${students.length}');

      // Apply filtering, searching, and sorting
      var filteredStudents = _applyFilter(students, filter);
      filteredStudents = _applySearch(filteredStudents, searchQuery);
      filteredStudents = _applySort(filteredStudents, sortOption);

      // Apply pagination
      final paginatedStudents = filteredStudents
          .skip(offset)
          .take(limit)
          .toList();

      return paginatedStudents;
    } catch (e) {
      debugPrint('❌ Error getting instructor students: $e');
      return [];
    }
  }

  // Helper methods for filtering, searching, and sorting
  List<InstructorStudent> _applyFilter(List<InstructorStudent> students, StudentFilter filter) {
    switch (filter) {
      case StudentFilter.all:
        return students;
      case StudentFilter.active:
        return students.where((s) => s.isOnline).toList();
      case StudentFilter.inactive:
        return students.where((s) => !s.isOnline).toList();
      case StudentFilter.newUsers:
        final oneWeekAgo = DateTime.now().subtract(const Duration(days: 7));
        return students.where((s) => s.joinedAt.isAfter(oneWeekAgo)).toList();
      case StudentFilter.premium:
        return students.where((s) => s.planType == StudentPlanType.premium).toList();
      case StudentFilter.basic:
        return students.where((s) => s.planType == StudentPlanType.basic).toList();
      case StudentFilter.onFire:
        return students.where((s) => s.activityScore > 0.8).toList();
    }
  }

  List<InstructorStudent> _applySearch(List<InstructorStudent> students, String? searchQuery) {
    if (searchQuery == null || searchQuery.isEmpty) return students;

    final query = searchQuery.toLowerCase();
    return students.where((student) {
      return student.name.toLowerCase().contains(query) ||
             student.email.toLowerCase().contains(query);
    }).toList();
  }

  List<InstructorStudent> _applySort(List<InstructorStudent> students, StudentSortOption sortOption) {
    switch (sortOption) {
      case StudentSortOption.nameAsc:
        students.sort((a, b) => a.name.compareTo(b.name));
        break;
      case StudentSortOption.nameDesc:
        students.sort((a, b) => b.name.compareTo(a.name));
        break;
      case StudentSortOption.joinedAsc:
        students.sort((a, b) => a.joinedAt.compareTo(b.joinedAt));
        break;
      case StudentSortOption.joinedDesc:
        students.sort((a, b) => b.joinedAt.compareTo(a.joinedAt));
        break;
      case StudentSortOption.activityAsc:
        students.sort((a, b) => a.activityScore.compareTo(b.activityScore));
        break;
      case StudentSortOption.activityDesc:
        students.sort((a, b) => b.activityScore.compareTo(a.activityScore));
        break;
      case StudentSortOption.pendingTasksDesc:
        students.sort((a, b) {
          final aPending = a.statusCounts.workoutPending +
                          a.statusCounts.cardioPending +
                          a.statusCounts.nutritionPending +
                          a.statusCounts.progressPhotosPending +
                          a.statusCounts.supplementPending;
          final bPending = b.statusCounts.workoutPending +
                          b.statusCounts.cardioPending +
                          b.statusCounts.nutritionPending +
                          b.statusCounts.progressPhotosPending +
                          b.statusCounts.supplementPending;
          return bPending.compareTo(aPending);
        });
        break;
    }
    return students;
  }

  // Helper methods for student status
  Future<StudentStatusCounts> _getStudentPlanStatus(String userId, String instructorId) async {
    try {
      debugPrint('🔍 Getting plan status for student: $userId, instructor: $instructorId');

      // Check workout plan status
      final workoutPlan = await _supabase
          .from('student_workout_plans')
          .select('id, plan_data, assigned_at, is_completed')
          .eq('student_id', userId)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .maybeSingle();

      // Check nutrition plan status
      final nutritionPlan = await _supabase
          .from('student_nutrition_plans')
          .select('id, plan_data, assigned_at, is_completed')
          .eq('student_id', userId)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .maybeSingle();

      // Check cardio plan status
      final cardioPlan = await _supabase
          .from('student_cardio_plans')
          .select('id, plan_data, assigned_at, is_completed')
          .eq('student_id', userId)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .maybeSingle();

      // Check supplement plan status
      final supplementPlan = await _supabase
          .from('student_supplement_plans')
          .select('id, supplements, assigned_at, is_completed')
          .eq('student_id', userId)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .maybeSingle();

      // Calculate pending and completed counts
      final workoutPending = _isPlanPending(workoutPlan, 'plan_data') ? 1 : 0;
      final workoutCompleted = _isPlanCompleted(workoutPlan) ? 1 : 0;

      final nutritionPending = _isPlanPending(nutritionPlan, 'plan_data') ? 1 : 0;
      final nutritionCompleted = _isPlanCompleted(nutritionPlan) ? 1 : 0;

      final cardioPending = _isPlanPending(cardioPlan, 'plan_data') ? 1 : 0;
      final cardioCompleted = _isPlanCompleted(cardioPlan) ? 1 : 0;

      final supplementPending = _isPlanPending(supplementPlan, 'supplements') ? 1 : 0;
      final supplementCompleted = _isPlanCompleted(supplementPlan) ? 1 : 0;

      // Progress photos are not mandatory tasks for now
      final progressPhotosPending = 0;
      final progressPhotosCompleted = 0;

      debugPrint('📊 Plan status - Workout: $workoutPending pending, Nutrition: $nutritionPending pending, Cardio: $cardioPending pending, Supplement: $supplementPending pending');

      return StudentStatusCounts(
        workoutPending: workoutPending,
        workoutCompleted: workoutCompleted,
        cardioPending: cardioPending,
        cardioCompleted: cardioCompleted,
        nutritionPending: nutritionPending,
        nutritionCompleted: nutritionCompleted,
        progressPhotosPending: progressPhotosPending,
        progressPhotosCompleted: progressPhotosCompleted,
        supplementPending: supplementPending,
        supplementCompleted: supplementCompleted,
      );
    } catch (e) {
      debugPrint('❌ Error getting student plan status: $e');
      return const StudentStatusCounts(
        workoutPending: 0,
        workoutCompleted: 0,
        cardioPending: 0,
        cardioCompleted: 0,
        nutritionPending: 0,
        nutritionCompleted: 0,
        progressPhotosPending: 0,
        progressPhotosCompleted: 0,
        supplementPending: 0,
        supplementCompleted: 0,
      );
    }
  }

  Future<double> _calculateActivityScore(String userId) async {
    // Mock implementation - replace with actual calculation
    return 0.5;
  }

  bool _isUserOnline(String? lastActivity) {
    if (lastActivity == null) return false;
    final lastActiveTime = DateTime.parse(lastActivity);
    final now = DateTime.now();
    return now.difference(lastActiveTime).inMinutes < 30;
  }

  /// Parse numeric value from database (handles both string and number types)
  double? _parseNumericValue(dynamic value) {
    if (value == null) return null;

    if (value is num) {
      return value.toDouble();
    }

    if (value is String) {
      return double.tryParse(value);
    }

    return null;
  }

  /// Check if a plan is pending (exists but has no content assigned by instructor)
  bool _isPlanPending(Map<String, dynamic>? plan, String contentField) {
    if (plan == null) return false; // No plan exists, so not pending

    final content = plan[contentField];

    // Check if content is empty or null
    if (content == null) return true;

    if (content is Map && content.isEmpty) return true;
    if (content is List && content.isEmpty) return true;
    if (content is String && content.trim().isEmpty) return true;

    return false; // Has content, so not pending
  }

  /// Check if a plan is completed
  bool _isPlanCompleted(Map<String, dynamic>? plan) {
    if (plan == null) return false;
    return plan['is_completed'] == true;
  }

  /// Get detailed student profile for profile detail page
  Future<StudentProfileDetail?> getStudentProfileDetail(
    String studentId,
    String instructorId,
  ) async {
    try {
      debugPrint('🔍 Getting detailed profile for student: $studentId');

      // First verify this student belongs to the instructor
      final enrollmentCheck = await _supabase
          .from('user_enrollments')
          .select('user_id')
          .eq('instructor_id', instructorId)
          .eq('user_id', studentId)
          .eq('is_active', true)
          .maybeSingle();

      if (enrollmentCheck == null) {
        debugPrint('❌ Student $studentId is not enrolled with instructor $instructorId');
        return null;
      }

      // Get basic profile info
      final profileResponse = await _supabase
          .from('profiles')
          .select('id, name, surname, email, avatar_url, updated_at')
          .eq('id', studentId)
          .maybeSingle();

      if (profileResponse == null) {
        debugPrint('❌ Profile not found for student: $studentId');
        return null;
      }

      // Get detailed profile info from user_profiles table
      final userProfileResponse = await _supabase
          .from('user_profiles')
          .select('fitness_goals, activity_level, dietary_restrictions, medical_conditions, additional_notes, weight, height, front_photo_url, side_photo_url, back_photo_url, created_at')
          .eq('user_id', studentId)
          .maybeSingle();

      debugPrint('🔍 User profile response: $userProfileResponse');

      // Get enrollment info
      final enrollmentResponse = await _supabase
          .from('user_enrollments')
          .select('enrolled_at')
          .eq('user_id', studentId)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .maybeSingle();

      debugPrint('✅ Successfully fetched detailed profile data');

      // Debug weight and height values
      final weightValue = userProfileResponse?['weight'];
      final heightValue = userProfileResponse?['height'];
      debugPrint('🔍 Raw weight value: $weightValue (type: ${weightValue.runtimeType})');
      debugPrint('🔍 Raw height value: $heightValue (type: ${heightValue.runtimeType})');

      final parsedWeight = _parseNumericValue(weightValue);
      final parsedHeight = _parseNumericValue(heightValue);
      debugPrint('🔍 Parsed weight: $parsedWeight');
      debugPrint('🔍 Parsed height: $parsedHeight');

      // Build the detailed profile
      final firstName = profileResponse['name'] as String? ?? '';
      final lastName = profileResponse['surname'] as String? ?? '';
      final fullName = '$firstName $lastName'.trim();

      // Get status counts and activity score
      final statusCounts = await _getStudentPlanStatus(studentId, instructorId);
      final activityScore = await _calculateActivityScore(studentId);

      return StudentProfileDetail(
        id: studentId,
        name: fullName.isNotEmpty ? fullName :
              (profileResponse['email'] as String? ?? 'Unknown').split('@')[0],
        email: profileResponse['email'] as String? ?? '',
        profileImageUrl: profileResponse['avatar_url'] as String?,
        joinedAt: enrollmentResponse != null
            ? DateTime.parse(enrollmentResponse['enrolled_at'] as String)
            : DateTime.now(),
        isOnline: _isUserOnline(profileResponse['updated_at'] as String?),
        planType: StudentPlanType.basic, // Default for now
        statusCounts: statusCounts,
        activityScore: activityScore,

        // User profile details
        fitnessGoals: userProfileResponse?['fitness_goals'] as String?,
        activityLevel: userProfileResponse?['activity_level'] as String?,
        dietaryRestrictions: (userProfileResponse?['dietary_restrictions'] as List<dynamic>?)
            ?.map((e) => e.toString()).toList() ?? [],
        medicalConditions: userProfileResponse?['medical_conditions'] as String?,
        additionalNotes: userProfileResponse?['additional_notes'] as String?,
        frontPhotoUrl: userProfileResponse?['front_photo_url'] as String?,
        sidePhotoUrl: userProfileResponse?['side_photo_url'] as String?,
        backPhotoUrl: userProfileResponse?['back_photo_url'] as String?,

        // Real weight and height from database
        weight: _parseNumericValue(userProfileResponse?['weight']) ?? 75.5, // Fallback for testing
        height: _parseNumericValue(userProfileResponse?['height']) ?? 180.0, // Fallback for testing
      );
    } catch (e) {
      debugPrint('❌ Error getting student profile detail: $e');
      return null;
    }
  }
}