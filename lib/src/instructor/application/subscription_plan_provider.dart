import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter/material.dart';
import '../domain/subscription_plan_models.dart';
import 'subscription_plan_repository.dart';
import 'instructor_provider.dart';

/// Provider for subscription plan configuration
final subscriptionPlanConfigProvider = FutureProvider<InstructorSubscriptionConfig>((ref) async {
  final instructorId = ref.watch(currentInstructorIdProvider);
  if (instructorId == null) {
    throw Exception('No instructor ID available');
  }

  final repository = ref.read(subscriptionPlanRepositoryProvider);
  return repository.getInstructorSubscriptionConfig(instructorId);
});

/// Provider for subscription plan state management
final subscriptionPlanNotifierProvider = 
    StateNotifierProvider<SubscriptionPlanNotifier, SubscriptionPlanState>((ref) {
  return SubscriptionPlanNotifier(ref);
});

/// State class for subscription plan management
class SubscriptionPlanState {
  final InstructorSubscriptionConfig? config;
  final bool isLoading;
  final String? error;
  final PlanDuration selectedDuration;
  final bool isSubmitting;
  final ValidationResult? validationResult;
  final bool showValidation;

  const SubscriptionPlanState({
    this.config,
    this.isLoading = false,
    this.error,
    this.selectedDuration = PlanDuration.monthly,
    this.isSubmitting = false,
    this.validationResult,
    this.showValidation = false,
  });

  SubscriptionPlanState copyWith({
    InstructorSubscriptionConfig? config,
    bool? isLoading,
    String? error,
    PlanDuration? selectedDuration,
    bool? isSubmitting,
    ValidationResult? validationResult,
    bool? showValidation,
  }) {
    return SubscriptionPlanState(
      config: config ?? this.config,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedDuration: selectedDuration ?? this.selectedDuration,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      validationResult: validationResult ?? this.validationResult,
      showValidation: showValidation ?? this.showValidation,
    );
  }
}

/// State notifier for subscription plan management
class SubscriptionPlanNotifier extends StateNotifier<SubscriptionPlanState> {
  final Ref _ref;

  SubscriptionPlanNotifier(this._ref) : super(const SubscriptionPlanState());

  /// Initialize subscription plan configuration
  Future<void> initializeConfig() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      final config = await repository.getInstructorSubscriptionConfig(instructorId);

      state = state.copyWith(
        config: config,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Update base monthly price
  void updateBaseMonthlyPrice(double price) {
    if (state.config == null) return;

    final repository = _ref.read(subscriptionPlanRepositoryProvider);
    
    // Recalculate all plan pricing based on new base price
    final basicPricing = <PlanDuration, PlanPricing>{};
    final premiumPricing = <PlanDuration, PlanPricing>{};

    for (final duration in PlanDuration.values) {
      basicPricing[duration] = repository.calculatePlanPricing(
        basePrice: price,
        duration: duration,
        planType: PlanType.basic,
      );
      
      premiumPricing[duration] = repository.calculatePlanPricing(
        basePrice: price,
        duration: duration,
        planType: PlanType.premium,
      );
    }

    final updatedConfig = state.config!.copyWith(
      baseMonthlyPrice: price,
      basicPlanPricing: basicPricing,
      premiumPlanPricing: premiumPricing,
    );

    state = state.copyWith(config: updatedConfig);
    _validateConfiguration();
  }

  /// Update premium plan custom pricing
  void updatePremiumPlanPricing(PlanDuration duration, double monthlyPrice) {
    if (state.config == null) return;

    final totalPrice = monthlyPrice * duration.months;
    final updatedPricing = PlanPricing(
      monthlyPrice: monthlyPrice,
      totalPrice: totalPrice,
      duration: duration,
      features: PlanFeatures.getFeaturesForPlan(PlanType.premium),
      isCustomPrice: true,
    );

    final updatedPremiumPricing = Map<PlanDuration, PlanPricing>.from(state.config!.premiumPlanPricing);
    updatedPremiumPricing[duration] = updatedPricing;

    final updatedConfig = state.config!.copyWith(
      premiumPlanPricing: updatedPremiumPricing,
    );

    state = state.copyWith(config: updatedConfig);
    _validateConfiguration();
  }

  /// Change selected duration tab
  void selectDuration(PlanDuration duration) {
    state = state.copyWith(selectedDuration: duration);
  }

  /// Add discount code
  Future<void> addDiscountCode(DiscountCode discountCode) async {
    if (state.config == null) return;

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return;

      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      final createdCode = await repository.createDiscountCode(instructorId, discountCode);

      final updatedCodes = [...state.config!.discountCodes, createdCode];
      final updatedConfig = state.config!.copyWith(discountCodes: updatedCodes);

      state = state.copyWith(config: updatedConfig);
      _validateConfiguration();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Remove discount code
  Future<void> removeDiscountCode(String discountCodeId) async {
    if (state.config == null) return;

    try {
      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      await repository.deleteDiscountCode(discountCodeId);

      final updatedCodes = state.config!.discountCodes
          .where((code) => code.id != discountCodeId)
          .toList();
      final updatedConfig = state.config!.copyWith(discountCodes: updatedCodes);

      state = state.copyWith(config: updatedConfig);
      _validateConfiguration();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Save configuration
  Future<void> saveConfiguration() async {
    if (state.config == null) return;

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return;

      state = state.copyWith(isLoading: true);

      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      await repository.saveInstructorSubscriptionConfig(instructorId, state.config!);

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Submit for review
  Future<bool> submitForReview() async {
    if (state.config == null) return false;

    // Validate before submission
    _validateConfiguration();
    if (state.validationResult?.isValid != true) {
      state = state.copyWith(showValidation: true);
      return false;
    }

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return false;

      state = state.copyWith(isSubmitting: true);

      final repository = _ref.read(subscriptionPlanRepositoryProvider);
      await repository.submitForReview(instructorId, state.config!);

      // Refresh config to get updated status
      await initializeConfig();

      state = state.copyWith(isSubmitting: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Show discount code creation dialog
  void showCreateDiscountCodeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _CreateDiscountCodeDialog(
        onCreateCode: (code) => addDiscountCode(code),
      ),
    );
  }

  /// Private validation method
  void _validateConfiguration() {
    if (state.config == null) return;

    final repository = _ref.read(subscriptionPlanRepositoryProvider);
    final validationResult = repository.validateConfiguration(state.config!);

    state = state.copyWith(validationResult: validationResult);
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Hide validation messages
  void hideValidation() {
    state = state.copyWith(showValidation: false);
  }
}

/// Provider for current plan pricing based on selected duration
final currentPlanPricingProvider = Provider<Map<PlanType, PlanPricing?>>((ref) {
  final planState = ref.watch(subscriptionPlanNotifierProvider);
  final selectedDuration = planState.selectedDuration;
  final config = planState.config;

  if (config == null) return {PlanType.basic: null, PlanType.premium: null};

  return {
    PlanType.basic: config.basicPlanPricing[selectedDuration],
    PlanType.premium: config.premiumPlanPricing[selectedDuration],
  };
});

/// Provider for submission button state
final submissionButtonStateProvider = Provider<SubmissionButtonState>((ref) {
  final planState = ref.watch(subscriptionPlanNotifierProvider);
  final config = planState.config;

  if (config == null) {
    return SubmissionButtonState(
      isEnabled: false,
      text: 'Loading...',
      isLoading: true,
    );
  }

  final canSubmit = config.submissionStatus.canSubmit;
  final isValid = planState.validationResult?.isValid ?? false;

  return SubmissionButtonState(
    isEnabled: canSubmit && isValid && !planState.isSubmitting,
    text: _getSubmissionButtonText(config.submissionStatus, planState.isSubmitting),
    isLoading: planState.isSubmitting,
    status: config.submissionStatus,
  );
});

String _getSubmissionButtonText(SubmissionStatus status, bool isSubmitting) {
  if (isSubmitting) return 'Submitting...';
  
  switch (status) {
    case SubmissionStatus.draft:
      return 'Submit for Review';
    case SubmissionStatus.rejected:
    case SubmissionStatus.needsRevision:
      return 'Resubmit for Review';
    case SubmissionStatus.submitted:
      return 'Submitted';
    case SubmissionStatus.underReview:
      return 'Under Review';
    case SubmissionStatus.approved:
      return 'Approved';
  }
}

/// Submission button state model
class SubmissionButtonState {
  final bool isEnabled;
  final String text;
  final bool isLoading;
  final SubmissionStatus? status;

  const SubmissionButtonState({
    required this.isEnabled,
    required this.text,
    this.isLoading = false,
    this.status,
  });
}

/// Create discount code dialog widget
class _CreateDiscountCodeDialog extends StatefulWidget {
  final Function(DiscountCode) onCreateCode;

  const _CreateDiscountCodeDialog({required this.onCreateCode});

  @override
  State<_CreateDiscountCodeDialog> createState() => _CreateDiscountCodeDialogState();
}

class _CreateDiscountCodeDialogState extends State<_CreateDiscountCodeDialog> {
  final _codeController = TextEditingController();
  final _discountController = TextEditingController();
  DateTime _expiryDate = DateTime.now().add(const Duration(days: 30));

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Discount Code'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _codeController,
            decoration: const InputDecoration(labelText: 'Code (e.g., SUMMER2024)'),
            textCapitalization: TextCapitalization.characters,
          ),
          TextField(
            controller: _discountController,
            decoration: const InputDecoration(labelText: 'Discount Percentage'),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          Text('Expiry Date: ${_expiryDate.toString().split(' ')[0]}'),
          ElevatedButton(
            onPressed: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: _expiryDate,
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 365)),
              );
              if (date != null) {
                setState(() => _expiryDate = date);
              }
            },
            child: const Text('Select Date'),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final code = _codeController.text.trim();
            final discount = double.tryParse(_discountController.text);
            
            if (code.isNotEmpty && discount != null && discount > 0 && discount <= 50) {
              final discountCode = DiscountCode(
                id: DateTime.now().millisecondsSinceEpoch.toString(),
                code: code,
                discountPercentage: discount,
                expiryDate: _expiryDate,
              );
              
              widget.onCreateCode(discountCode);
              Navigator.of(context).pop();
            }
          },
          child: const Text('Create'),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _codeController.dispose();
    _discountController.dispose();
    super.dispose();
  }
}
