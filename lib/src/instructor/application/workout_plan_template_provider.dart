import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../data/workout_plan_template_repository.dart';
import '../domain/workout_plan_template.dart';

// Repository provider
final workoutPlanTemplateRepositoryProvider = Provider<WorkoutPlanTemplateRepository>((ref) {
  return WorkoutPlanTemplateRepository(Supabase.instance.client);
});

// Workout plan templates provider
final workoutPlanTemplatesProvider = FutureProvider<List<WorkoutPlanTemplate>>((ref) async {
  final repository = ref.read(workoutPlanTemplateRepositoryProvider);
  return repository.getWorkoutPlanTemplates(instructorId: '8c9ca0c0-4435-4b14-a57e-07a706b39c4d');
});

// Workout plan templates provider with instructor ID parameter
final workoutPlanTemplatesWithInstructorProvider = FutureProvider.family<List<WorkoutPlanTemplate>, String>((ref, instructorId) async {
  final repository = ref.read(workoutPlanTemplateRepositoryProvider);
  return repository.getWorkoutPlanTemplates(instructorId: instructorId);
});

// Exercise filters state provider
final exerciseFiltersProvider = StateProvider<ExerciseFilters>((ref) {
  return const ExerciseFilters();
});

// Filtered exercises provider
final filteredExercisesProvider = FutureProvider<List<Exercise>>((ref) async {
  final filters = ref.watch(exerciseFiltersProvider);
  final repository = ref.read(workoutPlanTemplateRepositoryProvider);
  return repository.getExercises(
    categoryFilter: filters.categoryFilter,
    searchQuery: filters.searchQuery,
  );
});

// Muscle groups provider for category filtering
final muscleGroupsProvider = Provider<List<String>>((ref) {
  return [
    'All',
    'chest',
    'back',
    'shoulders',
    'arms',
    'legs',
    'core',
    'glutes',
    'cardio',
  ];
});

// Exercises provider with filters (legacy - keeping for compatibility)
final exercisesProvider = FutureProvider.family<List<Exercise>, ExerciseFilters>((ref, filters) async {
  final repository = ref.read(workoutPlanTemplateRepositoryProvider);
  return repository.getExercises(
    categoryFilter: filters.categoryFilter,
    searchQuery: filters.searchQuery,
  );
});

// State notifier for managing workout plan template operations
final workoutPlanTemplateNotifierProvider = StateNotifierProvider<WorkoutPlanTemplateNotifier, AsyncValue<List<WorkoutPlanTemplate>>>((ref) {
  final repository = ref.read(workoutPlanTemplateRepositoryProvider);
  return WorkoutPlanTemplateNotifier(repository);
});

class WorkoutPlanTemplateNotifier extends StateNotifier<AsyncValue<List<WorkoutPlanTemplate>>> {
  final WorkoutPlanTemplateRepository _repository;

  WorkoutPlanTemplateNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadTemplates();
  }

  Future<void> loadTemplates({String? instructorId}) async {
    state = const AsyncValue.loading();
    try {
      final templates = await _repository.getWorkoutPlanTemplates(
        instructorId: instructorId ?? '8c9ca0c0-4435-4b14-a57e-07a706b39c4d',
      );
      state = AsyncValue.data(templates);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> createTemplate({
    required String name,
    String? description,
    List<String>? tags,
    String? notes,
  }) async {
    try {
      await _repository.createWorkoutPlanTemplate(
        name: name,
        description: description,
        tags: tags,
        notes: notes,
      );
      await loadTemplates(); // Refresh the list
    } catch (error) {
      // Handle error - could emit to a separate error state
      rethrow;
    }
  }

  Future<void> updateTemplate({
    required String templateId,
    String? name,
    String? description,
    List<String>? tags,
    String? notes,
  }) async {
    try {
      await _repository.updateWorkoutPlanTemplate(
        templateId: templateId,
        name: name,
        description: description,
        tags: tags,
        notes: notes,
      );
      await loadTemplates(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deleteTemplate(String templateId) async {
    try {
      await _repository.deleteWorkoutPlanTemplate(templateId);
      await loadTemplates(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  Future<void> addPlanToTemplate({
    required String templateId,
    required String name,
    String? description,
  }) async {
    try {
      await _repository.addPlanToTemplate(
        templateId: templateId,
        name: name,
        description: description,
      );
      await loadTemplates(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  Future<void> deletePlanFromTemplate(String planId) async {
    try {
      await _repository.deletePlanFromTemplate(planId);
      await loadTemplates(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  Future<void> addExerciseToPlan({
    required String planId,
    required String exerciseId,
    int? sets,
    int? reps,
    double? weight,
    int? duration,
    int? restTime,
    String? notes,
  }) async {
    try {
      await _repository.addExerciseToPlan(
        planId: planId,
        exerciseId: exerciseId,
        sets: sets,
        reps: reps,
        weight: weight,
        duration: duration,
        restTime: restTime,
        notes: notes,
      );
      await loadTemplates(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }

  Future<void> removeExerciseFromPlan(String exerciseId) async {
    try {
      await _repository.removeExerciseFromPlan(exerciseId);
      await loadTemplates(); // Refresh the list
    } catch (error) {
      rethrow;
    }
  }
}

// Exercise filters class
class ExerciseFilters {
  final String? categoryFilter;
  final String? searchQuery;

  const ExerciseFilters({
    this.categoryFilter,
    this.searchQuery,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExerciseFilters &&
          runtimeType == other.runtimeType &&
          categoryFilter == other.categoryFilter &&
          searchQuery == other.searchQuery;

  @override
  int get hashCode => categoryFilter.hashCode ^ searchQuery.hashCode;
}

// Available tags for templates
final availableTagsProvider = Provider<List<String>>((ref) {
  return [
    'Strength',
    'Cardio',
    'HIIT',
    'Flexibility',
    'Beginner',
    'Intermediate',
    'Advanced',
    'Full Body',
    'Upper Body',
    'Lower Body',
    'Core',
    'Endurance',
    'Power',
    'Hypertrophy',
  ];
});
