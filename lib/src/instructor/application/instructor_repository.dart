import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import '../domain/instructor_models.dart';

/// Provider for instructor repository
final instructorRepositoryProvider = Provider<InstructorRepository>((ref) {
  return InstructorRepository();
});

/// Repository for instructor-related data operations
class InstructorRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Get instructor dashboard data
  Future<InstructorDashboard> getInstructorDashboard(String instructorId) async {
    try {
      // Get instructor profile
      final profile = await _getInstructorProfile(instructorId);
      
      // Get instructor statistics
      final stats = await _getInstructorStats(instructorId);
      
      // Get priority alerts
      final alerts = await _getPriorityAlerts(instructorId);
      
      // Get task overview
      final taskOverview = await _getTaskOverview(instructorId);
      
      // Get new students
      final newStudents = await _getNewStudents(instructorId);
      
      // Get weekly progress
      final weeklyProgress = await _getWeeklyProgress(instructorId);

      return InstructorDashboard(
        profile: profile,
        stats: stats,
        priorityAlerts: alerts,
        taskOverview: taskOverview,
        newStudents: newStudents,
        weeklyProgress: weeklyProgress,
      );
    } catch (e) {
      // Return mock data for development
      return _getMockDashboard();
    }
  }

  /// Get instructor profile information
  Future<InstructorProfile> _getInstructorProfile(String instructorId) async {
    final response = await _supabase
        .from('instructors')
        .select('id, name, title, photo_url, email, experience_years, specializations')
        .eq('id', instructorId)
        .single();

    return InstructorProfile(
      id: response['id'] as String,
      name: response['name'] as String,
      title: response['title'] as String? ?? 'Fitness Trainer',
      avatarUrl: response['photo_url'] as String?,
      email: response['email'] as String,
      experienceYears: response['experience_years'] as int? ?? 0,
      specializations: List<String>.from(response['specializations'] ?? []),
    );
  }

  /// Get instructor statistics
  Future<InstructorStats> _getInstructorStats(String instructorId) async {
    // Get total students count
    final totalStudentsResponse = await _supabase
        .from('user_enrollments')
        .select('id')
        .eq('instructor_id', instructorId)
        .eq('is_active', true);

    final totalStudents = totalStudentsResponse.length;

    // Get new students today
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    
    final newStudentsTodayResponse = await _supabase
        .from('user_enrollments')
        .select('id')
        .eq('instructor_id', instructorId)
        .gte('enrolled_at', startOfDay.toIso8601String());

    final newStudentsToday = newStudentsTodayResponse.length;

    // Get instructor rating
    final ratingResponse = await _supabase
        .from('trainer_reviews')
        .select('rating')
        .eq('trainer_id', instructorId);

    double averageRating = 0.0;
    if (ratingResponse.isNotEmpty) {
      final ratings = ratingResponse.map((r) => r['rating'] as num).toList();
      averageRating = ratings.reduce((a, b) => a + b) / ratings.length;
    }

    return InstructorStats(
      totalStudents: totalStudents,
      newStudentsToday: newStudentsToday,
      newStudentsChange: 2, // Mock change value
      averageRating: averageRating,
      totalReviews: ratingResponse.length,
    );
  }

  /// Get priority alerts for instructor
  Future<List<PriorityAlert>> _getPriorityAlerts(String instructorId) async {
    // This would typically query a dedicated alerts table
    // For now, return mock data
    return [
      PriorityAlert(
        id: '1',
        studentId: 'student_1',
        studentName: 'Sarah Johnson',
        message: "hasn't checked in for 7 days",
        type: AlertType.inactivity,
        actionLabel: 'Contact',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      PriorityAlert(
        id: '2',
        studentId: 'student_2',
        studentName: 'Michael Chen',
        message: "needs workout update",
        type: AlertType.workoutUpdate,
        actionLabel: 'Update',
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
    ];
  }

  /// Get task overview for instructor
  Future<TaskOverview> _getTaskOverview(String instructorId) async {
    try {
      debugPrint('🔍 Getting task overview for instructor: $instructorId');

      // Get all students for this instructor
      final enrollments = await _supabase
          .from('user_enrollments')
          .select('user_id')
          .eq('instructor_id', instructorId)
          .eq('is_active', true);

      if (enrollments.isEmpty) {
        debugPrint('📊 No students found for instructor');
        return const TaskOverview(
          workouts: 0,
          cardio: 0,
          nutrition: 0,
          feedback: 0,
          questions: 0,
          supplements: 0,
        );
      }

      final studentIds = enrollments.map((e) => e['user_id'] as String).toList();
      debugPrint('📊 Checking tasks for ${studentIds.length} students');

      // Count pending workout plans (plans with empty plan_data)
      final workoutTasks = await _supabase
          .from('student_workout_plans')
          .select('id')
          .inFilter('student_id', studentIds)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .or('plan_data.is.null,plan_data.eq.{}');

      // Count pending nutrition plans
      final nutritionTasks = await _supabase
          .from('student_nutrition_plans')
          .select('id')
          .inFilter('student_id', studentIds)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .or('plan_data.is.null,plan_data.eq.{}');

      // Count pending cardio plans
      final cardioTasks = await _supabase
          .from('student_cardio_plans')
          .select('id')
          .inFilter('student_id', studentIds)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .or('plan_data.is.null,plan_data.eq.{}');

      // Count pending supplement plans (plans with empty supplements array)
      final supplementTasks = await _supabase
          .from('student_supplement_plans')
          .select('id')
          .inFilter('student_id', studentIds)
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .or('supplements.is.null,supplements.eq.[]');

      // Feedback and questions are not automatically created, so they remain 0 for now
      final feedbackTasks = 0;
      final questionTasks = 0;

      debugPrint('📊 Task counts - Workout: ${workoutTasks.length}, Nutrition: ${nutritionTasks.length}, Cardio: ${cardioTasks.length}, Supplement: ${supplementTasks.length}');

      return TaskOverview(
        workouts: workoutTasks.length,
        cardio: cardioTasks.length,
        nutrition: nutritionTasks.length,
        feedback: feedbackTasks,
        questions: questionTasks,
        supplements: supplementTasks.length,
      );
    } catch (e) {
      debugPrint('❌ Error getting task overview: $e');
      // Return mock data on error
      return const TaskOverview(
        workouts: 0,
        cardio: 0,
        nutrition: 0,
        feedback: 0,
        questions: 0,
        supplements: 0,
      );
    }
  }

  /// Get new students for instructor
  Future<List<NewStudent>> _getNewStudents(String instructorId) async {
    try {
      // Try user_enrollments first
      var response = await _supabase
          .from('user_enrollments')
          .select('''
            user_id,
            enrolled_at,
            profiles(
              id,
              name,
              surname,
              email,
              avatar_url
            )
          ''')
          .eq('instructor_id', instructorId)
          .eq('is_active', true)
          .order('enrolled_at', ascending: false)
          .limit(5);

      return response.map<NewStudent>((enrollment) {
        final profile = enrollment['profiles'];
        final firstName = profile['name'] as String? ?? '';
        final lastName = profile['surname'] as String? ?? '';
        final fullName = '$firstName $lastName'.trim();

        return NewStudent(
          id: profile['id'] as String,
          name: fullName.isNotEmpty ? fullName :
                (profile['email'] as String? ?? 'Unknown').split('@')[0],
          avatarUrl: profile['avatar_url'] as String?,
          joinedAt: DateTime.parse(enrollment['enrolled_at'] as String),
          programType: 'Standard', // Would come from enrollment data
          hasCompletedProfile: true, // Would check profile completion
        );
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting new students: $e');
      // Fallback to student_profiles table
      try {
        final response = await _supabase
            .from('student_profiles')
            .select('''
              user_id,
              created_at,
              profiles(
                id,
                name,
                surname,
                email,
                avatar_url
              )
            ''')
            .eq('current_instructor_id', instructorId)
            .order('created_at', ascending: false)
            .limit(5);

        return response.map<NewStudent>((studentProfile) {
          final profile = studentProfile['profiles'];
          final firstName = profile['name'] as String? ?? '';
          final lastName = profile['surname'] as String? ?? '';
          final fullName = '$firstName $lastName'.trim();

          return NewStudent(
            id: profile['id'] as String,
            name: fullName.isNotEmpty ? fullName :
                  (profile['email'] as String? ?? 'Unknown').split('@')[0],
            avatarUrl: profile['avatar_url'] as String?,
            joinedAt: DateTime.parse(studentProfile['created_at'] as String),
            programType: 'Standard',
            hasCompletedProfile: true,
          );
        }).toList();
      } catch (e2) {
        debugPrint('❌ Error getting students from student_profiles table: $e2');
        return [];
      }
    }
  }

  /// Get weekly progress summary
  Future<WeeklyProgress> _getWeeklyProgress(String instructorId) async {
    // This would aggregate data from various tables
    // For now, return mock data
    return const WeeklyProgress(
      totalCompletedTasks: 156,
      missedWorkouts: 8,
      inactiveStudents: 3,
      completionRate: 0.85,
    );
  }

  /// Get mock dashboard data for development
  InstructorDashboard _getMockDashboard() {
    return InstructorDashboard(
      profile: const InstructorProfile(
        id: 'instructor_1',
        name: 'James Wilson',
        title: 'Head Trainer',
        avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
        email: '<EMAIL>',
        experienceYears: 8,
        specializations: ['Strength Training', 'Weight Loss', 'Nutrition'],
      ),
      stats: const InstructorStats(
        totalStudents: 48,
        newStudentsToday: 6,
        newStudentsChange: 2,
        averageRating: 4.8,
        totalReviews: 124,
      ),
      priorityAlerts: [
        PriorityAlert(
          id: '1',
          studentId: 'student_1',
          studentName: 'Sarah Johnson',
          message: "hasn't checked in for 7 days",
          type: AlertType.inactivity,
          actionLabel: 'Contact',
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        PriorityAlert(
          id: '2',
          studentId: 'student_2',
          studentName: 'Michael Chen',
          message: "needs workout update",
          type: AlertType.workoutUpdate,
          actionLabel: 'Update',
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        ),
      ],
      taskOverview: const TaskOverview(
        workouts: 12,
        cardio: 8,
        nutrition: 15,
        feedback: 6,
        questions: 4,
        supplements: 3,
      ),
      newStudents: [
        NewStudent(
          id: 'student_3',
          name: 'Emma Davis',
          avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
          joinedAt: DateTime.now().subtract(const Duration(hours: 2)),
          programType: 'Premium',
          hasCompletedProfile: true,
        ),
        NewStudent(
          id: 'student_4',
          name: 'Alex Rodriguez',
          avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
          joinedAt: DateTime.now().subtract(const Duration(hours: 5)),
          programType: 'Standard',
          hasCompletedProfile: false,
        ),
      ],
      weeklyProgress: const WeeklyProgress(
        totalCompletedTasks: 156,
        missedWorkouts: 8,
        inactiveStudents: 3,
        completionRate: 0.85,
      ),
    );
  }

  /// Handle priority alert action
  Future<void> handleAlertAction(String alertId, String action) async {
    // Implementation for handling alert actions
    // This would update the alert status and trigger appropriate actions
  }

  /// Create tasks for new student enrollment
  Future<void> createTasksForNewStudent({
    required String instructorId,
    required String studentId,
    required String programType,
  }) async {
    final tasks = [
      {
        'instructor_id': instructorId,
        'student_id': studentId,
        'task_type': 'workout',
        'title': 'Create Workout Plan',
        'description': 'Design initial workout plan for new student',
        'status': 'pending',
        'priority': 'high',
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'instructor_id': instructorId,
        'student_id': studentId,
        'task_type': 'cardio',
        'title': 'Create Cardio Plan',
        'description': 'Design cardio routine for new student',
        'status': 'pending',
        'priority': 'medium',
        'created_at': DateTime.now().toIso8601String(),
      },
      {
        'instructor_id': instructorId,
        'student_id': studentId,
        'task_type': 'nutrition',
        'title': 'Create Nutrition Plan',
        'description': 'Design nutrition plan for new student',
        'status': 'pending',
        'priority': 'high',
        'created_at': DateTime.now().toIso8601String(),
      },
    ];

    // Add supplements task for premium students
    if (programType.toLowerCase().contains('premium')) {
      tasks.add({
        'instructor_id': instructorId,
        'student_id': studentId,
        'task_type': 'supplement',
        'title': 'Create Supplements Plan',
        'description': 'Recommend supplements for new premium student',
        'status': 'pending',
        'priority': 'low',
        'created_at': DateTime.now().toIso8601String(),
      });
    }

    // Insert tasks into database
    await _supabase.from('instructor_tasks').insert(tasks);
  }
}
