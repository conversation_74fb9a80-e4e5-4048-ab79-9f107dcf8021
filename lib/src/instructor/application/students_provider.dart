import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter/material.dart';
import '../domain/student_models.dart';
import '../presentation/student_profile_detail_screen.dart';
import '../presentation/assign_exercise_to_student_page.dart';
import '../presentation/assign_nutrition_to_student_page.dart';
import 'students_repository.dart';
import 'instructor_provider.dart';
import '../../shared/utils/app_logger.dart';

/// Provider for instructor students list
final instructorStudentsProvider = FutureProvider<List<InstructorStudent>>((
  ref,
) async {
  final instructorId = ref.watch(currentInstructorIdProvider);
  if (instructorId == null) {
    throw Exception('No instructor ID available');
  }

  final repository = ref.read(studentsRepositoryProvider);
  return repository.getInstructorStudents(instructorId);
});

/// Provider for students list state management
final studentsListNotifierProvider =
    StateNotifierProvider<StudentsListNotifier, StudentsListState>((ref) {
      return StudentsListNotifier(ref);
    });

/// State class for students list management
class StudentsListState {
  final List<InstructorStudent> students;
  final List<InstructorStudent> filteredStudents;
  final bool isLoading;
  final String? error;
  final String searchQuery;
  final StudentFilter selectedFilter;
  final StudentSortOption selectedSort;
  final bool isLoadingMore;
  final bool hasMoreData;
  final int currentPage;

  const StudentsListState({
    this.students = const [],
    this.filteredStudents = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
    this.selectedFilter = StudentFilter.all,
    this.selectedSort = StudentSortOption.activityDesc,
    this.isLoadingMore = false,
    this.hasMoreData = true,
    this.currentPage = 0,
  });

  StudentsListState copyWith({
    List<InstructorStudent>? students,
    List<InstructorStudent>? filteredStudents,
    bool? isLoading,
    String? error,
    String? searchQuery,
    StudentFilter? selectedFilter,
    StudentSortOption? selectedSort,
    bool? isLoadingMore,
    bool? hasMoreData,
    int? currentPage,
  }) {
    return StudentsListState(
      students: students ?? this.students,
      filteredStudents: filteredStudents ?? this.filteredStudents,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      selectedSort: selectedSort ?? this.selectedSort,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

/// State notifier for students list management
class StudentsListNotifier extends StateNotifier<StudentsListState> {
  final Ref _ref;

  StudentsListNotifier(this._ref) : super(const StudentsListState());

  /// Initialize students list
  Future<void> initializeStudents() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      debugPrint('🔍 Current instructor ID: $instructorId');

      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      final repository = _ref.read(studentsRepositoryProvider);
      final students = await repository.getInstructorStudents(
        instructorId,
        searchQuery: state.searchQuery,
        filter: state.selectedFilter,
        sortOption: state.selectedSort,
      );

      debugPrint('📊 Students fetched: ${students.length}');

      state = state.copyWith(
        students: students,
        filteredStudents: students,
        isLoading: false,
        currentPage: 1,
        hasMoreData: students.length >= 50, // Assuming page size of 50
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Refresh students list
  Future<void> refreshStudents() async {
    state = state.copyWith(currentPage: 0);
    await initializeStudents();
  }

  /// Load more students (pagination)
  Future<void> loadMoreStudents() async {
    if (state.isLoadingMore || !state.hasMoreData) return;

    state = state.copyWith(isLoadingMore: true);

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return;

      final repository = _ref.read(studentsRepositoryProvider);
      final newStudents = await repository.getInstructorStudents(
        instructorId,
        searchQuery: state.searchQuery,
        filter: state.selectedFilter,
        sortOption: state.selectedSort,
        offset: state.currentPage * 50,
      );

      final allStudents = [...state.students, ...newStudents];

      state = state.copyWith(
        students: allStudents,
        filteredStudents: allStudents,
        isLoadingMore: false,
        currentPage: state.currentPage + 1,
        hasMoreData: newStudents.length >= 50,
      );
    } catch (e) {
      state = state.copyWith(isLoadingMore: false, error: e.toString());
    }
  }

  /// Update search query
  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
    _applyFiltersAndSearch();
  }

  /// Update selected filter
  void updateFilter(StudentFilter filter) {
    state = state.copyWith(selectedFilter: filter);
    _applyFiltersAndSearch();
  }

  /// Update selected sort option
  void updateSort(StudentSortOption sortOption) {
    state = state.copyWith(selectedSort: sortOption);
    _applyFiltersAndSearch();
  }

  /// Apply filters and search to current students list
  void _applyFiltersAndSearch() {
    var filteredStudents = List<InstructorStudent>.from(state.students);

    // Apply search
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filteredStudents =
          filteredStudents.where((student) {
            return student.name.toLowerCase().contains(query) ||
                student.email.toLowerCase().contains(query);
          }).toList();
    }

    // Apply filter
    switch (state.selectedFilter) {
      case StudentFilter.all:
        break;
      case StudentFilter.active:
        filteredStudents =
            filteredStudents.where((s) => s.activityScore >= 50).toList();
        break;
      case StudentFilter.inactive:
        filteredStudents =
            filteredStudents.where((s) => s.activityScore < 50).toList();
        break;
      case StudentFilter.newUsers:
        filteredStudents = filteredStudents.where((s) => s.isNewUser).toList();
        break;
      case StudentFilter.premium:
        filteredStudents =
            filteredStudents
                .where((s) => s.planType == StudentPlanType.premium)
                .toList();
        break;
      case StudentFilter.basic:
        filteredStudents =
            filteredStudents
                .where((s) => s.planType == StudentPlanType.basic)
                .toList();
        break;
      case StudentFilter.onFire:
        filteredStudents = filteredStudents.where((s) => s.isOnFire).toList();
        break;
    }

    // Apply sorting
    switch (state.selectedSort) {
      case StudentSortOption.activityDesc:
        filteredStudents.sort(
          (a, b) => b.activityScore.compareTo(a.activityScore),
        );
        break;
      case StudentSortOption.activityAsc:
        filteredStudents.sort(
          (a, b) => a.activityScore.compareTo(b.activityScore),
        );
        break;
      case StudentSortOption.nameAsc:
        filteredStudents.sort((a, b) => a.name.compareTo(b.name));
        break;
      case StudentSortOption.nameDesc:
        filteredStudents.sort((a, b) => b.name.compareTo(a.name));
        break;
      case StudentSortOption.joinedDesc:
        filteredStudents.sort((a, b) => b.joinedAt.compareTo(a.joinedAt));
        break;
      case StudentSortOption.joinedAsc:
        filteredStudents.sort((a, b) => a.joinedAt.compareTo(b.joinedAt));
        break;
      case StudentSortOption.pendingTasksDesc:
        filteredStudents.sort(
          (a, b) => b.totalPendingTasks.compareTo(a.totalPendingTasks),
        );
        break;
    }

    state = state.copyWith(filteredStudents: filteredStudents);
  }

  /// Navigate to student detail page
  void navigateToStudentDetail(BuildContext context, String studentId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StudentProfileDetailScreen(studentId: studentId),
      ),
    );
  }

  /// Navigate to specific program management
  void navigateToProgramManagement(
    BuildContext context,
    String studentId,
    StudentProgramType programType,
  ) {
    // Get the student data
    final student = state.filteredStudents.firstWhere(
      (s) => s.id == studentId,
      orElse: () => throw Exception('Student not found'),
    );

    switch (programType) {
      case StudentProgramType.workout:
        // Navigate to assign exercise page
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => AssignExerciseToStudentPage(
                  student: student,
                  instructorId:
                      '8c9ca0c0-4435-4b14-a57e-07a706b39c4d', // TODO: Get from auth
                ),
          ),
        );
        break;
      case StudentProgramType.nutrition:
        // Navigate to nutrition assignment page
        AppLogger.userAction(
          'Navigating to nutrition assignment for student: ${student.name}',
          tag: 'STUDENTS_PROVIDER',
        );
        Navigator.of(context)
            .push(
              MaterialPageRoute(
                builder:
                    (context) => AssignNutritionToStudentPage(
                      student: student,
                      instructorId:
                          '8c9ca0c0-4435-4b14-a57e-07a706b39c4d', // TODO: Get from auth
                    ),
              ),
            )
            .then((_) {
              // Refresh students list when returning from assignment
              AppLogger.info(
                'Returned from nutrition assignment, refreshing students',
                tag: 'STUDENTS_PROVIDER',
              );
              refreshStudents();
            });
        break;
      case StudentProgramType.cardio:
        // TODO: Navigate to cardio assignment
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cardio assignment - Coming Soon!'),
            backgroundColor: Color(0xFFFACC15),
          ),
        );
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${programType.displayName} management - Coming Soon!',
            ),
            backgroundColor: const Color(0xFFFACC15),
          ),
        );
    }
  }

  /// Show filter bottom sheet
  void showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF1F2937),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => _FilterBottomSheet(
            currentFilter: state.selectedFilter,
            currentSort: state.selectedSort,
            onFilterChanged: updateFilter,
            onSortChanged: updateSort,
          ),
    );
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for students statistics
final studentsStatsProvider = Provider<StudentsStats>((ref) {
  final studentsState = ref.watch(studentsListNotifierProvider);
  final students = studentsState.filteredStudents;

  if (students.isEmpty) {
    return const StudentsStats(
      totalStudents: 0,
      activeStudents: 0,
      newStudents: 0,
      premiumStudents: 0,
      totalPendingTasks: 0,
    );
  }

  return StudentsStats(
    totalStudents: students.length,
    activeStudents: students.where((s) => s.activityScore >= 50).length,
    newStudents: students.where((s) => s.isNewUser).length,
    premiumStudents:
        students.where((s) => s.planType == StudentPlanType.premium).length,
    totalPendingTasks: students.fold(
      0,
      (sum, student) => sum + student.totalPendingTasks,
    ),
  );
});

/// Provider for student profile detail
final studentProfileDetailProvider =
    FutureProvider.family<StudentProfileDetail?, String>((
      ref,
      studentId,
    ) async {
      final repository = ref.read(studentsRepositoryProvider);
      final instructorId = ref.read(currentInstructorIdProvider);

      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      return repository.getStudentProfileDetail(studentId, instructorId);
    });

/// Students statistics model
class StudentsStats {
  final int totalStudents;
  final int activeStudents;
  final int newStudents;
  final int premiumStudents;
  final int totalPendingTasks;

  const StudentsStats({
    required this.totalStudents,
    required this.activeStudents,
    required this.newStudents,
    required this.premiumStudents,
    required this.totalPendingTasks,
  });
}

class _FilterBottomSheet extends StatelessWidget {
  final StudentFilter currentFilter;
  final StudentSortOption currentSort;
  final Function(StudentFilter) onFilterChanged;
  final Function(StudentSortOption) onSortChanged;

  const _FilterBottomSheet({
    required this.currentFilter,
    required this.currentSort,
    required this.onFilterChanged,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return Container(
      padding: const EdgeInsets.all(20),
      // Tüm sheet'in de kaydırılabilmesi için SingleChildScrollView
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filter & Sort Students',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // --- FILTER SECTION ---
            const Text(
              'Filter by:',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  StudentFilter.values.map((filter) {
                    final isSelected = filter == currentFilter;
                    return GestureDetector(
                      onTap: () {
                        onFilterChanged(filter);
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? const Color(0xFFFACC15)
                                  : Colors.transparent,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color:
                                isSelected
                                    ? const Color(0xFFFACC15)
                                    : Colors.grey,
                          ),
                        ),
                        child: Text(
                          filter.displayName,
                          style: TextStyle(
                            color: isSelected ? Colors.black : Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),

            const SizedBox(height: 20),

            // --- SORT SECTION ---
            const Text(
              'Sort by:',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 10),

            // Bu Container içinde kaydırılabilir liste
            Container(
              // Örneğin ekranın %30'u kadar yer kaplasın
              constraints: BoxConstraints(maxHeight: screenHeight * 0.3),
              decoration: BoxDecoration(
                color: const Color(0xFF374151), // Opsiyonel arka plan
                borderRadius: BorderRadius.circular(12),
              ),
              child: ListView(
                shrinkWrap: true,
                children:
                    StudentSortOption.values.map((sort) {
                      final isSelected = sort == currentSort;
                      return ListTile(
                        title: Text(
                          sort.displayName,
                          style: TextStyle(
                            color:
                                isSelected
                                    ? const Color(0xFFFACC15)
                                    : Colors.white,
                          ),
                        ),
                        leading: Radio<StudentSortOption>(
                          value: sort,
                          groupValue: currentSort,
                          onChanged: (value) {
                            if (value != null) {
                              onSortChanged(value);
                              Navigator.of(context).pop();
                            }
                          },
                          activeColor: const Color(0xFFFACC15),
                        ),
                        onTap: () {
                          onSortChanged(sort);
                          Navigator.of(context).pop();
                        },
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Filter bottom sheet widget
/* class _FilterBottomSheet extends StatelessWidget {
  final StudentFilter currentFilter;
  final StudentSortOption currentSort;
  final Function(StudentFilter) onFilterChanged;
  final Function(StudentSortOption) onSortChanged;

  const _FilterBottomSheet({
    required this.currentFilter,
    required this.currentSort,
    required this.onFilterChanged,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filter & Sort Students',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          // Filter Section
          const Text(
            'Filter by:',
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
          const SizedBox(height: 10),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: StudentFilter.values.map((filter) {
              final isSelected = filter == currentFilter;
              return GestureDetector(
                onTap: () {
                  onFilterChanged(filter);
                  Navigator.of(context).pop();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFFFACC15) : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? const Color(0xFFFACC15) : Colors.grey,
                    ),
                  ),
                  child: Text(
                    filter.displayName,
                    style: TextStyle(
                      color: isSelected ? Colors.black : Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          
          const SizedBox(height: 20),
          
          // Sort Section
          const Text(
            'Sort by:',
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
          const SizedBox(height: 10),
          ...StudentSortOption.values.map((sort) {
            final isSelected = sort == currentSort;
            return ListTile(
              title: Text(
                sort.displayName,
                style: TextStyle(
                  color: isSelected ? const Color(0xFFFACC15) : Colors.white,
                ),
              ),
              leading: Radio<StudentSortOption>(
                value: sort,
                groupValue: currentSort,
                onChanged: (value) {
                  if (value != null) {
                    onSortChanged(value);
                    Navigator.of(context).pop();
                  }
                },
                activeColor: const Color(0xFFFACC15),
              ),
              onTap: () {
                onSortChanged(sort);
                Navigator.of(context).pop();
              },
            );
          }),
        ],
      ),
    );
  }
} */
