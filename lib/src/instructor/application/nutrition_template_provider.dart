import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/nutrition_template.dart';
import '../data/nutrition_template_repository.dart';

// Repository provider
final nutritionTemplateRepositoryProvider = Provider<NutritionTemplateRepository>((ref) {
  return NutritionTemplateRepository();
});

// Nutrition templates provider
final nutritionTemplatesProvider = FutureProvider<List<NutritionTemplate>>((ref) async {
  final repository = ref.read(nutritionTemplateRepositoryProvider);
  return repository.getNutritionTemplates(
    instructorId: '8c9ca0c0-4435-4b14-a57e-07a706b39c4d',
  );
});

// State notifier for managing nutrition template operations
final nutritionTemplateNotifierProvider = StateNotifierProvider<NutritionTemplateNotifier, AsyncValue<List<NutritionTemplate>>>((ref) {
  final repository = ref.read(nutritionTemplateRepositoryProvider);
  return NutritionTemplateNotifier(repository);
});

class NutritionTemplateNotifier extends StateNotifier<AsyncValue<List<NutritionTemplate>>> {
  final NutritionTemplateRepository _repository;

  NutritionTemplateNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadTemplates();
  }

  Future<void> loadTemplates() async {
    state = const AsyncValue.loading();
    try {
      final templates = await _repository.getNutritionTemplates(
        instructorId: '8c9ca0c0-4435-4b14-a57e-07a706b39c4d',
      );
      state = AsyncValue.data(templates);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }



  // Create a new nutrition template
  Future<NutritionTemplate?> createNutritionTemplate(NutritionTemplate template) async {
    try {
      print('🔄 Provider: Creating template with ${template.meals.length} meals');
      final createdTemplate = await _repository.createNutritionTemplate(template);

      // Reload templates to get updated list
      print('🔄 Provider: Reloading templates after creation');
      await loadTemplates();
      print('✅ Provider: Templates reloaded, notifying listeners');

      return createdTemplate;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  // Update a nutrition template
  Future<NutritionTemplate?> updateNutritionTemplate(NutritionTemplate template) async {
    try {
      final updatedTemplate = await _repository.updateNutritionTemplate(template);

      // Reload templates to get updated list
      await loadTemplates();

      return updatedTemplate;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return null;
    }
  }

  // Delete a nutrition template
  Future<bool> deleteNutritionTemplate(String templateId) async {
    try {
      await _repository.deleteNutritionTemplate(templateId);

      // Reload templates to get updated list
      await loadTemplates();

      return true;
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
      return false;
    }
  }
}
