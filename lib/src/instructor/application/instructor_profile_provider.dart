import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter/material.dart';
import '../domain/instructor_profile_models.dart';
import 'instructor_profile_repository.dart';
import 'instructor_provider.dart';
import 'instructor_registration_service.dart';
import '../presentation/components/profile_edit_dialogs.dart';

/// Provider for instructor profile data
final instructorProfileDataProvider = FutureProvider<InstructorProfileData>((ref) async {
  final instructorId = ref.watch(currentInstructorIdProvider);
  if (instructorId == null) {
    throw Exception('No instructor ID available');
  }

  final repository = ref.read(instructorProfileRepositoryProvider);
  return repository.getInstructorProfile(instructorId);
});

/// Provider for instructor profile state management
final instructorProfileNotifierProvider = 
    StateNotifierProvider<InstructorProfileNotifier, InstructorProfileState>((ref) {
  return InstructorProfileNotifier(ref);
});

/// State class for instructor profile
class InstructorProfileState {
  final InstructorProfileData? profileData;
  final bool isLoading;
  final String? error;
  final List<FAQ> expandedFAQs;
  final bool isEditing;
  final ProfileSection? editingSection;

  const InstructorProfileState({
    this.profileData,
    this.isLoading = false,
    this.error,
    this.expandedFAQs = const [],
    this.isEditing = false,
    this.editingSection,
  });

  InstructorProfileState copyWith({
    InstructorProfileData? profileData,
    bool? isLoading,
    String? error,
    List<FAQ>? expandedFAQs,
    bool? isEditing,
    ProfileSection? editingSection,
  }) {
    return InstructorProfileState(
      profileData: profileData ?? this.profileData,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      expandedFAQs: expandedFAQs ?? this.expandedFAQs,
      isEditing: isEditing ?? this.isEditing,
      editingSection: editingSection,
    );
  }
}

/// State notifier for instructor profile
class InstructorProfileNotifier extends StateNotifier<InstructorProfileState> {
  final Ref _ref;

  InstructorProfileNotifier(this._ref) : super(const InstructorProfileState());

  /// Initialize profile data
  Future<void> initializeProfile() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) {
        throw Exception('No instructor ID available');
      }

      // Ensure instructor record exists before loading profile
      final registrationService = _ref.read(instructorRegistrationServiceProvider);
      await registrationService.ensureInstructorRecord(instructorId);

      final repository = _ref.read(instructorProfileRepositoryProvider);
      final profileData = await repository.getInstructorProfile(instructorId);

      state = state.copyWith(
        profileData: profileData,
        isLoading: false,
        expandedFAQs: profileData.faqs,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Refresh profile data
  Future<void> refreshProfile() async {
    await initializeProfile();
  }

  /// Toggle FAQ expansion
  void toggleFAQ(String faqId) {
    final updatedFAQs = state.expandedFAQs.map((faq) {
      if (faq.id == faqId) {
        return faq.copyWith(isExpanded: !faq.isExpanded);
      }
      return faq;
    }).toList();

    state = state.copyWith(expandedFAQs: updatedFAQs);
  }

  /// Start editing a section
  void startEditing(ProfileSection section) {
    state = state.copyWith(
      isEditing: true,
      editingSection: section,
    );
  }

  /// Stop editing
  void stopEditing() {
    state = state.copyWith(
      isEditing: false,
      editingSection: null,
    );
  }

  /// Handle profile edit actions
  Future<void> handleEditAction(ProfileEditAction action, {Map<String, dynamic>? data}) async {
    try {
      final instructorId = _ref.read(currentInstructorIdProvider);
      if (instructorId == null) return;

      final repository = _ref.read(instructorProfileRepositoryProvider);

      switch (action) {
        case ProfileEditAction.editBasicInfo:
          if (data?['basicInfo'] != null) {
            final basicInfo = data!['basicInfo'] as InstructorBasicInfo;
            await repository.updateBasicInfo(instructorId, basicInfo);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.addWorkExperience:
          if (data?['workExperience'] != null) {
            final workExperience = data!['workExperience'] as WorkExperience;
            await repository.addWorkExperience(instructorId, workExperience);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.editWorkExperience:
          if (data?['workExperience'] != null) {
            final workExperience = data!['workExperience'] as WorkExperience;
            await repository.updateWorkExperience(instructorId, workExperience);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.deleteWorkExperience:
          if (data?['workExperienceId'] != null) {
            await repository.deleteWorkExperience(data!['workExperienceId'] as String);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.addCertification:
          if (data?['certification'] != null) {
            final certification = data!['certification'] as Certification;
            await repository.addCertification(instructorId, certification);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.editCertification:
          if (data?['certification'] != null) {
            final certification = data!['certification'] as Certification;
            await repository.updateCertification(instructorId, certification);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.deleteCertification:
          if (data?['certificationId'] != null) {
            await repository.deleteCertification(data!['certificationId'] as String);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.addFAQ:
          if (data?['faq'] != null) {
            final faq = data!['faq'] as FAQ;
            await repository.addFAQ(instructorId, faq);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.editFAQ:
          if (data?['faq'] != null) {
            final faq = data!['faq'] as FAQ;
            await repository.updateFAQ(instructorId, faq);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.deleteFAQ:
          if (data?['faqId'] != null) {
            await repository.deleteFAQ(data!['faqId'] as String);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.deleteReview:
          if (data?['reviewId'] != null) {
            await repository.deleteReview(data!['reviewId'] as String);
            await refreshProfile();
          }
          break;
        case ProfileEditAction.manageSubscription:
          // Handle subscription management
          break;
        default:
          break;
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Navigate to subscription management
  void navigateToSubscriptionManagement(BuildContext context) {
    // TODO: Implement navigation to subscription management
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening subscription management...'),
        backgroundColor: Color(0xFFFACC15),
      ),
    );
  }

  /// Show edit dialog
  void showEditDialog(BuildContext context, ProfileEditAction action, {dynamic data}) {
    switch (action) {
      case ProfileEditAction.editBasicInfo:
        if (state.profileData?.basicInfo != null) {
          showDialog(
            context: context,
            builder: (context) => BasicInfoEditDialog(
              basicInfo: state.profileData!.basicInfo,
            ),
          );
        }
        break;
      case ProfileEditAction.addWorkExperience:
        showDialog(
          context: context,
          builder: (context) => const WorkExperienceEditDialog(),
        );
        break;
      case ProfileEditAction.editWorkExperience:
        if (data is WorkExperience) {
          showDialog(
            context: context,
            builder: (context) => WorkExperienceEditDialog(experience: data),
          );
        }
        break;
      case ProfileEditAction.addCertification:
        showDialog(
          context: context,
          builder: (context) => const CertificationEditDialog(),
        );
        break;
      case ProfileEditAction.editCertification:
        if (data is Certification) {
          showDialog(
            context: context,
            builder: (context) => CertificationEditDialog(certification: data),
          );
        }
        break;
      case ProfileEditAction.addFAQ:
        showDialog(
          context: context,
          builder: (context) => const FAQEditDialog(),
        );
        break;
      case ProfileEditAction.editFAQ:
        if (data is FAQ) {
          showDialog(
            context: context,
            builder: (context) => FAQEditDialog(faq: data),
          );
        }
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Opening ${action.name} editor...'),
            backgroundColor: const Color(0xFFFACC15),
          ),
        );
        break;
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for instructor badges
final instructorBadgesProvider = Provider<List<InstructorBadge>>((ref) {
  final profileData = ref.watch(instructorProfileDataProvider).value;
  if (profileData == null) return [];

  final basicInfo = profileData.basicInfo;
  
  return [
    InstructorBadge(
      label: 'Experience',
      value: '${basicInfo.experienceYears} years',
      icon: '🏆',
      type: BadgeType.experience,
    ),
    InstructorBadge(
      label: 'Students',
      value: '${basicInfo.totalStudents}',
      icon: '👥',
      type: BadgeType.students,
    ),
    InstructorBadge(
      label: 'Certification',
      value: basicInfo.primaryCertification,
      icon: '📜',
      type: BadgeType.certification,
    ),
  ];
});

/// Provider for profile completion percentage
final profileCompletionProvider = Provider<double>((ref) {
  final profileData = ref.watch(instructorProfileDataProvider).value;
  if (profileData == null) return 0.0;

  int completedSections = 0;
  int totalSections = 6;

  // Basic info is always completed if we have data
  completedSections++;

  // Work history
  if (profileData.workHistory.isNotEmpty) completedSections++;

  // Certifications
  if (profileData.certifications.isNotEmpty) completedSections++;

  // Reviews (not controlled by instructor but counts for completion)
  if (profileData.reviews.isNotEmpty) completedSections++;

  // FAQs
  if (profileData.faqs.isNotEmpty) completedSections++;

  // Subscription
  if (profileData.subscriptionInfo != null) completedSections++;

  return completedSections / totalSections;
});

/// Provider for review statistics
final reviewStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final profileData = ref.watch(instructorProfileDataProvider).value;
  if (profileData == null) return {};

  final reviews = profileData.reviews;
  if (reviews.isEmpty) return {};

  // Calculate rating distribution
  final ratingCounts = <int, int>{};
  for (int i = 1; i <= 5; i++) {
    ratingCounts[i] = 0;
  }

  for (final review in reviews) {
    final rating = review.rating.round();
    ratingCounts[rating] = (ratingCounts[rating] ?? 0) + 1;
  }

  return {
    'total': reviews.length,
    'average': profileData.basicInfo.averageRating,
    'distribution': ratingCounts,
    'recent': reviews.take(3).toList(),
  };
});

/// Provider for empty state messages
final emptyStateMessagesProvider = Provider<Map<ProfileSection, String>>((ref) {
  return {
    ProfileSection.workHistory: 'Add your work experience to showcase your professional background',
    ProfileSection.certifications: 'Add certifications to build trust with potential clients',
    ProfileSection.reviews: 'Client reviews will appear here once you start training students',
    ProfileSection.faqs: 'Add frequently asked questions to help potential clients learn more about you',
  };
});
