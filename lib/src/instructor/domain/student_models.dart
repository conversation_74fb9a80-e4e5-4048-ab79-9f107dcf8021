// Domain models for instructor's student management

/// Complete student information for instructor view
class InstructorStudent {
  final String id;
  final String name;
  final String email;
  final String? profileImageUrl;
  final DateTime joinedAt;
  final bool isOnline;
  final StudentPlanType planType;
  final List<StudentTag> tags;
  final StudentStatusCounts statusCounts;
  final DateTime? lastActiveAt;
  final double activityScore;

  const InstructorStudent({
    required this.id,
    required this.name,
    required this.email,
    this.profileImageUrl,
    required this.joinedAt,
    this.isOnline = false,
    required this.planType,
    this.tags = const [],
    required this.statusCounts,
    this.lastActiveAt,
    this.activityScore = 0.0,
  });

  /// Get initials for avatar fallback
  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : '?';
  }

  /// Check if student is new (joined within last 7 days)
  bool get isNewUser {
    return DateTime.now().difference(joinedAt).inDays <= 7;
  }

  /// Check if student is highly active (activity score > 80)
  bool get isOnFire {
    return activityScore >= 80.0;
  }

  /// Get all applicable tags
  List<StudentTag> get allTags {
    final allTags = <StudentTag>[...tags];
    
    if (isNewUser) {
      allTags.add(StudentTag.newUser);
    }
    
    if (isOnFire) {
      allTags.add(StudentTag.onFire);
    }
    
    if (planType == StudentPlanType.premium) {
      allTags.add(StudentTag.premium);
    }
    
    return allTags;
  }

  /// Get total pending tasks count
  int get totalPendingTasks {
    return statusCounts.workoutPending +
           statusCounts.cardioPending +
           statusCounts.nutritionPending +
           statusCounts.progressPhotosPending +
           statusCounts.supplementPending;
  }
}

/// Student plan type
enum StudentPlanType {
  basic,
  premium;

  String get displayName {
    switch (this) {
      case StudentPlanType.basic:
        return 'Basic';
      case StudentPlanType.premium:
        return 'Premium';
    }
  }
}

/// Student tags for visual indicators
enum StudentTag {
  onFire,
  newUser,
  premium,
  inactive,
  vip;

  String get emoji {
    switch (this) {
      case StudentTag.onFire:
        return '🔥';
      case StudentTag.newUser:
        return '🆕';
      case StudentTag.premium:
        return '👑';
      case StudentTag.inactive:
        return '😴';
      case StudentTag.vip:
        return '⭐';
    }
  }

  String get label {
    switch (this) {
      case StudentTag.onFire:
        return 'On Fire';
      case StudentTag.newUser:
        return 'New User';
      case StudentTag.premium:
        return 'Premium';
      case StudentTag.inactive:
        return 'Inactive';
      case StudentTag.vip:
        return 'VIP';
    }
  }

  String get color {
    switch (this) {
      case StudentTag.onFire:
        return '#FF4444'; // Red
      case StudentTag.newUser:
        return '#10B981'; // Green
      case StudentTag.premium:
        return '#FACC15'; // Gold
      case StudentTag.inactive:
        return '#6B7280'; // Gray
      case StudentTag.vip:
        return '#8B5CF6'; // Purple
    }
  }
}

/// Status counts for different program areas
class StudentStatusCounts {
  final int workoutPending;
  final int workoutCompleted;
  final int cardioPending;
  final int cardioCompleted;
  final int nutritionPending;
  final int nutritionCompleted;
  final int progressPhotosPending;
  final int progressPhotosCompleted;
  final int supplementPending;
  final int supplementCompleted;

  const StudentStatusCounts({
    this.workoutPending = 0,
    this.workoutCompleted = 0,
    this.cardioPending = 0,
    this.cardioCompleted = 0,
    this.nutritionPending = 0,
    this.nutritionCompleted = 0,
    this.progressPhotosPending = 0,
    this.progressPhotosCompleted = 0,
    this.supplementPending = 0,
    this.supplementCompleted = 0,
  });

  /// Get status info for a specific program type
  StatusInfo getStatusInfo(StudentProgramType programType) {
    switch (programType) {
      case StudentProgramType.workout:
        return StatusInfo(
          pending: workoutPending,
          completed: workoutCompleted,
          icon: '🏋️‍♂️',
          label: 'Workout',
        );
      case StudentProgramType.cardio:
        return StatusInfo(
          pending: cardioPending,
          completed: cardioCompleted,
          icon: '🏃‍♂️',
          label: 'Cardio',
        );
      case StudentProgramType.nutrition:
        return StatusInfo(
          pending: nutritionPending,
          completed: nutritionCompleted,
          icon: '🍏',
          label: 'Nutrition',
        );
      case StudentProgramType.progressPhotos:
        return StatusInfo(
          pending: progressPhotosPending,
          completed: progressPhotosCompleted,
          icon: '📸',
          label: 'Progress Photos',
        );
      case StudentProgramType.supplement:
        return StatusInfo(
          pending: supplementPending,
          completed: supplementCompleted,
          icon: '📦',
          label: 'Supplements',
        );
    }
  }
}

/// Program types for students
enum StudentProgramType {
  workout,
  cardio,
  nutrition,
  progressPhotos,
  supplement;

  String get displayName {
    switch (this) {
      case StudentProgramType.workout:
        return 'Workout';
      case StudentProgramType.cardio:
        return 'Cardio';
      case StudentProgramType.nutrition:
        return 'Nutrition';
      case StudentProgramType.progressPhotos:
        return 'Progress Photos';
      case StudentProgramType.supplement:
        return 'Supplements';
    }
  }
}

/// Status information for a program type
class StatusInfo {
  final int pending;
  final int completed;
  final String icon;
  final String label;

  const StatusInfo({
    required this.pending,
    required this.completed,
    required this.icon,
    required this.label,
  });

  bool get hasPendingTasks => pending > 0;
  int get total => pending + completed;
}

/// Filter options for student list
enum StudentFilter {
  all,
  active,
  inactive,
  newUsers,
  premium,
  basic,
  onFire;

  String get displayName {
    switch (this) {
      case StudentFilter.all:
        return 'All Students';
      case StudentFilter.active:
        return 'Active';
      case StudentFilter.inactive:
        return 'Inactive';
      case StudentFilter.newUsers:
        return 'New Users';
      case StudentFilter.premium:
        return 'Premium';
      case StudentFilter.basic:
        return 'Basic';
      case StudentFilter.onFire:
        return 'On Fire';
    }
  }
}

/// Sort options for student list
enum StudentSortOption {
  activityDesc,
  activityAsc,
  nameAsc,
  nameDesc,
  joinedDesc,
  joinedAsc,
  pendingTasksDesc;

  String get displayName {
    switch (this) {
      case StudentSortOption.activityDesc:
        return 'Most Active';
      case StudentSortOption.activityAsc:
        return 'Least Active';
      case StudentSortOption.nameAsc:
        return 'Name A-Z';
      case StudentSortOption.nameDesc:
        return 'Name Z-A';
      case StudentSortOption.joinedDesc:
        return 'Recently Joined';
      case StudentSortOption.joinedAsc:
        return 'Oldest Members';
      case StudentSortOption.pendingTasksDesc:
        return 'Most Pending Tasks';
    }
  }
}

/// Detailed student profile for profile detail page
class StudentProfileDetail {
  final String id;
  final String name;
  final String email;
  final String? profileImageUrl;
  final DateTime joinedAt;
  final bool isOnline;
  final StudentPlanType planType;
  final List<StudentTag> tags;
  final StudentStatusCounts statusCounts;
  final DateTime? lastActiveAt;
  final double activityScore;

  // Additional profile details from user_profiles table
  final String? fitnessGoals;
  final String? activityLevel;
  final List<String> dietaryRestrictions;
  final String? medicalConditions;
  final String? additionalNotes;
  final String? frontPhotoUrl;
  final String? sidePhotoUrl;
  final String? backPhotoUrl;

  // Mock data for weight and height as requested
  final double? weight; // kg
  final double? height; // cm

  const StudentProfileDetail({
    required this.id,
    required this.name,
    required this.email,
    this.profileImageUrl,
    required this.joinedAt,
    this.isOnline = false,
    required this.planType,
    this.tags = const [],
    required this.statusCounts,
    this.lastActiveAt,
    this.activityScore = 0.0,
    this.fitnessGoals,
    this.activityLevel,
    this.dietaryRestrictions = const [],
    this.medicalConditions,
    this.additionalNotes,
    this.frontPhotoUrl,
    this.sidePhotoUrl,
    this.backPhotoUrl,
    this.weight,
    this.height,
  });

  /// Get initials for avatar fallback
  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : '?';
  }

  /// Check if student is new (joined within last 7 days)
  bool get isNewUser {
    return DateTime.now().difference(joinedAt).inDays <= 7;
  }

  /// Check if student is highly active (activity score > 80)
  bool get isOnFire {
    return activityScore >= 80.0;
  }

  /// Get membership duration string
  String get membershipDuration {
    final duration = DateTime.now().difference(joinedAt);
    if (duration.inDays < 30) {
      return 'Member for ${duration.inDays} days';
    } else if (duration.inDays < 365) {
      final months = (duration.inDays / 30).floor();
      return 'Member for $months month${months > 1 ? 's' : ''}';
    } else {
      final years = (duration.inDays / 365).floor();
      return 'Member for $years year${years > 1 ? 's' : ''}';
    }
  }

  /// Get all applicable tags
  List<StudentTag> get allTags {
    final allTags = <StudentTag>[...tags];

    if (isNewUser) {
      allTags.add(StudentTag.newUser);
    }

    if (isOnFire) {
      allTags.add(StudentTag.onFire);
    }

    if (planType == StudentPlanType.premium) {
      allTags.add(StudentTag.premium);
    }

    return allTags;
  }

  /// Get formatted dietary restrictions as preferences
  List<String> get preferences {
    final prefs = <String>[];

    // Add dietary restrictions
    for (final restriction in dietaryRestrictions) {
      switch (restriction) {
        case 'vegetarian':
          prefs.add('Vegetarian');
          break;
        case 'glutenFree':
          prefs.add('Gluten Free');
          break;
        case 'dairyFree':
          prefs.add('Dairy Free');
          break;
        default:
          prefs.add(restriction);
      }
    }

    // Add activity level as preference
    if (activityLevel != null) {
      switch (activityLevel) {
        case 'sedentary':
          prefs.add('Low Activity');
          break;
        case 'lightlyActive':
          prefs.add('Light Activity');
          break;
        case 'moderatelyActive':
          prefs.add('Moderate Activity');
          break;
        default:
          prefs.add(activityLevel!);
      }
    }

    // Add some mock fitness preferences
    prefs.addAll(['High Protein', 'Strength Training']);

    return prefs;
  }

  /// Check if student has uploaded photos
  bool get hasPhotos {
    return frontPhotoUrl != null || sidePhotoUrl != null || backPhotoUrl != null;
  }

  /// Get list of available photo URLs
  List<String> get photoUrls {
    final urls = <String>[];
    if (frontPhotoUrl != null) urls.add(frontPhotoUrl!);
    if (sidePhotoUrl != null) urls.add(sidePhotoUrl!);
    if (backPhotoUrl != null) urls.add(backPhotoUrl!);
    return urls;
  }
}
