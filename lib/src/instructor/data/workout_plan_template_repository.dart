import 'package:supabase_flutter/supabase_flutter.dart';
import '../domain/workout_plan_template.dart';

class WorkoutPlanTemplateRepository {
  final SupabaseClient _supabase;

  WorkoutPlanTemplateRepository(this._supabase);

  /// Get all workout plan templates for the current instructor
  Future<List<WorkoutPlanTemplate>> getWorkoutPlanTemplates({String? instructorId}) async {
    try {
      final currentInstructorId = instructorId ?? _supabase.auth.currentUser!.id;
      print('🏋️ Fetching workout templates for instructor: $currentInstructorId');

      final response = await _supabase
          .from('workout_plan_templates')
          .select('''
            *,
            workout_template_plans!inner(
              *,
              workout_template_plan_exercises(
                *,
                exercises(
                  id,
                  name,
                  description,
                  muscle_groups,
                  equipment,
                  difficulty_level,
                  image_url
                )
              )
            )
          ''')
          .eq('instructor_id', currentInstructorId)
          .order('updated_at', ascending: false);

      return (response as List).map((json) {
        final template = Map<String, dynamic>.from(json);
        final plans = (template['workout_template_plans'] as List? ?? [])
            .map((planJson) {
          final plan = Map<String, dynamic>.from(planJson);
          final exercises = (plan['workout_template_plan_exercises'] as List? ?? [])
              .map((exerciseJson) {
            final exercise = Map<String, dynamic>.from(exerciseJson);
            final exerciseDetails = exercise['exercises'] as Map<String, dynamic>?;
            
            return WorkoutTemplatePlanExercise(
              id: exercise['id'],
              planId: exercise['plan_id'],
              exerciseId: exercise['exercise_id'],
              orderIndex: exercise['order_index'] ?? 0,
              sets: exercise['sets'],
              reps: exercise['reps'],
              weight: exercise['weight']?.toDouble(),
              duration: exercise['duration'],
              restTime: exercise['rest_time'],
              notes: exercise['notes'],
              createdAt: DateTime.parse(exercise['created_at']),
              exerciseName: exerciseDetails?['name'],
              exerciseDescription: exerciseDetails?['description'],
              muscleGroups: (exerciseDetails?['muscle_groups'] as List?)?.cast<String>(),
              equipment: (exerciseDetails?['equipment'] as List?)?.cast<String>(),
              difficultyLevel: exerciseDetails?['difficulty_level'],
              imageUrl: exerciseDetails?['image_url'],
            );
          }).toList();

          return WorkoutTemplatePlan(
            id: plan['id'],
            templateId: plan['template_id'],
            name: plan['name'],
            description: plan['description'],
            orderIndex: plan['order_index'] ?? 0,
            createdAt: DateTime.parse(plan['created_at']),
            updatedAt: DateTime.parse(plan['updated_at']),
            exercises: exercises,
          );
        }).toList();

        return WorkoutPlanTemplate(
          id: template['id'],
          instructorId: template['instructor_id'],
          name: template['name'],
          description: template['description'],
          tags: (template['tags'] as List?)?.cast<String>() ?? [],
          notes: template['notes'],
          isDraft: template['is_draft'] ?? true,
          createdAt: DateTime.parse(template['created_at']),
          updatedAt: DateTime.parse(template['updated_at']),
          plans: plans,
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch workout plan templates: $e');
    }
  }

  /// Create a new workout plan template
  Future<WorkoutPlanTemplate> createWorkoutPlanTemplate({
    required String name,
    String? description,
    List<String>? tags,
    String? notes,
  }) async {
    try {
      final response = await _supabase
          .from('workout_plan_templates')
          .insert({
            'instructor_id': _supabase.auth.currentUser!.id,
            'name': name,
            'description': description,
            'tags': tags ?? [],
            'notes': notes,
            'is_draft': true,
          })
          .select()
          .single();

      // Create a default plan
      final planResponse = await _supabase
          .from('workout_template_plans')
          .insert({
            'template_id': response['id'],
            'name': 'Plan 1',
            'order_index': 0,
          })
          .select()
          .single();

      return WorkoutPlanTemplate(
        id: response['id'],
        instructorId: response['instructor_id'],
        name: response['name'],
        description: response['description'],
        tags: (response['tags'] as List?)?.cast<String>() ?? [],
        notes: response['notes'],
        isDraft: response['is_draft'] ?? true,
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
        plans: [
          WorkoutTemplatePlan(
            id: planResponse['id'],
            templateId: planResponse['template_id'],
            name: planResponse['name'],
            description: planResponse['description'],
            orderIndex: planResponse['order_index'] ?? 0,
            createdAt: DateTime.parse(planResponse['created_at']),
            updatedAt: DateTime.parse(planResponse['updated_at']),
            exercises: [],
          ),
        ],
      );
    } catch (e) {
      throw Exception('Failed to create workout plan template: $e');
    }
  }

  /// Update a workout plan template
  Future<WorkoutPlanTemplate> updateWorkoutPlanTemplate({
    required String templateId,
    String? name,
    String? description,
    List<String>? tags,
    String? notes,
  }) async {
    try {
      final response = await _supabase
          .from('workout_plan_templates')
          .update({
            if (name != null) 'name': name,
            if (description != null) 'description': description,
            if (tags != null) 'tags': tags,
            if (notes != null) 'notes': notes,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', templateId)
          .select()
          .single();

      // Fetch the updated template with all relations
      final templates = await getWorkoutPlanTemplates();
      return templates.firstWhere((t) => t.id == templateId);
    } catch (e) {
      throw Exception('Failed to update workout plan template: $e');
    }
  }

  /// Delete a workout plan template
  Future<void> deleteWorkoutPlanTemplate(String templateId) async {
    try {
      await _supabase
          .from('workout_plan_templates')
          .delete()
          .eq('id', templateId);
    } catch (e) {
      throw Exception('Failed to delete workout plan template: $e');
    }
  }

  /// Get all exercises for the exercise selector
  Future<List<Exercise>> getExercises({String? categoryFilter, String? searchQuery}) async {
    try {
      var query = _supabase.from('exercises').select();

      if (categoryFilter != null && categoryFilter != 'All') {
        // Filter by muscle group or category
        query = query.contains('muscle_groups', [categoryFilter.toLowerCase()]);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.ilike('name', '%$searchQuery%');
      }

      final response = await query.order('name');

      return (response as List).map((json) {
        return Exercise(
          id: json['id'],
          name: json['name'],
          description: json['description'],
          instructions: json['instructions'],
          categoryId: json['category_id'],
          muscleGroups: (json['muscle_groups'] as List?)?.cast<String>() ?? [],
          equipment: (json['equipment'] as List?)?.cast<String>() ?? [],
          difficultyLevel: json['difficulty_level'],
          videoUrl: json['video_url'],
          imageUrl: json['image_url'],
          caloriesPerMinute: json['calories_per_minute']?.toDouble(),
          createdAt: DateTime.parse(json['created_at']),
          updatedAt: DateTime.parse(json['updated_at']),
        );
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch exercises: $e');
    }
  }

  /// Add a plan to a template
  Future<WorkoutTemplatePlan> addPlanToTemplate({
    required String templateId,
    required String name,
    String? description,
  }) async {
    try {
      // Get the current max order index
      final existingPlans = await _supabase
          .from('workout_template_plans')
          .select('order_index')
          .eq('template_id', templateId)
          .order('order_index', ascending: false)
          .limit(1);

      final nextOrderIndex = existingPlans.isEmpty ? 0 : (existingPlans.first['order_index'] ?? 0) + 1;

      final response = await _supabase
          .from('workout_template_plans')
          .insert({
            'template_id': templateId,
            'name': name,
            'description': description,
            'order_index': nextOrderIndex,
          })
          .select()
          .single();

      return WorkoutTemplatePlan(
        id: response['id'],
        templateId: response['template_id'],
        name: response['name'],
        description: response['description'],
        orderIndex: response['order_index'] ?? 0,
        createdAt: DateTime.parse(response['created_at']),
        updatedAt: DateTime.parse(response['updated_at']),
        exercises: [],
      );
    } catch (e) {
      throw Exception('Failed to add plan to template: $e');
    }
  }

  /// Delete a plan from a template
  Future<void> deletePlanFromTemplate(String planId) async {
    try {
      await _supabase
          .from('workout_template_plans')
          .delete()
          .eq('id', planId);
    } catch (e) {
      throw Exception('Failed to delete plan from template: $e');
    }
  }

  /// Add an exercise to a plan
  Future<WorkoutTemplatePlanExercise> addExerciseToPlan({
    required String planId,
    required String exerciseId,
    int? sets,
    int? reps,
    double? weight,
    int? duration,
    int? restTime,
    String? notes,
  }) async {
    try {
      // Get the current max order index
      final existingExercises = await _supabase
          .from('workout_template_plan_exercises')
          .select('order_index')
          .eq('plan_id', planId)
          .order('order_index', ascending: false)
          .limit(1);

      final nextOrderIndex = existingExercises.isEmpty ? 0 : (existingExercises.first['order_index'] ?? 0) + 1;

      final response = await _supabase
          .from('workout_template_plan_exercises')
          .insert({
            'plan_id': planId,
            'exercise_id': exerciseId,
            'order_index': nextOrderIndex,
            'sets': sets,
            'reps': reps,
            'weight': weight,
            'duration': duration,
            'rest_time': restTime,
            'notes': notes,
          })
          .select('''
            *,
            exercises(
              id,
              name,
              description,
              muscle_groups,
              equipment,
              difficulty_level,
              image_url
            )
          ''')
          .single();

      final exerciseDetails = response['exercises'] as Map<String, dynamic>;

      return WorkoutTemplatePlanExercise(
        id: response['id'],
        planId: response['plan_id'],
        exerciseId: response['exercise_id'],
        orderIndex: response['order_index'] ?? 0,
        sets: response['sets'],
        reps: response['reps'],
        weight: response['weight']?.toDouble(),
        duration: response['duration'],
        restTime: response['rest_time'],
        notes: response['notes'],
        createdAt: DateTime.parse(response['created_at']),
        exerciseName: exerciseDetails['name'],
        exerciseDescription: exerciseDetails['description'],
        muscleGroups: (exerciseDetails['muscle_groups'] as List?)?.cast<String>(),
        equipment: (exerciseDetails['equipment'] as List?)?.cast<String>(),
        difficultyLevel: exerciseDetails['difficulty_level'],
        imageUrl: exerciseDetails['image_url'],
      );
    } catch (e) {
      throw Exception('Failed to add exercise to plan: $e');
    }
  }

  /// Remove an exercise from a plan
  Future<void> removeExerciseFromPlan(String exerciseId) async {
    try {
      await _supabase
          .from('workout_template_plan_exercises')
          .delete()
          .eq('id', exerciseId);
    } catch (e) {
      throw Exception('Failed to remove exercise from plan: $e');
    }
  }
}
