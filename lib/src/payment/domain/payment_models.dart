// Domain models for payment feature
class OrderSummary {
  final String trainerId;
  final String trainerName;
  final String packageId;
  final String packageName;
  final String duration;
  final double basePrice;
  final double? discountAmount;
  final double? promoDiscount;
  final double totalPrice;
  final String currency;

  const OrderSummary({
    required this.trainerId,
    required this.trainerName,
    required this.packageId,
    required this.packageName,
    required this.duration,
    required this.basePrice,
    this.discountAmount,
    this.promoDiscount,
    required this.totalPrice,
    this.currency = '₺',
  });

  double get finalPrice {
    double price = basePrice;
    if (discountAmount != null) price -= discountAmount!;
    if (promoDiscount != null) price -= promoDiscount!;
    return price.clamp(0.0, double.infinity);
  }

  bool get hasDiscounts => discountAmount != null || promoDiscount != null;

  factory OrderSummary.fromJson(Map<String, dynamic> json) {
    return OrderSummary(
      trainerId: json['trainer_id'] as String,
      trainerName: json['trainer_name'] as String,
      packageId: json['package_id'] as String,
      packageName: json['package_name'] as String,
      duration: json['duration'] as String,
      basePrice: (json['base_price'] as num).toDouble(),
      discountAmount: (json['discount_amount'] as num?)?.toDouble(),
      promoDiscount: (json['promo_discount'] as num?)?.toDouble(),
      totalPrice: (json['total_price'] as num).toDouble(),
      currency: json['currency'] as String? ?? '₺',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trainer_id': trainerId,
      'trainer_name': trainerName,
      'package_id': packageId,
      'package_name': packageName,
      'duration': duration,
      'base_price': basePrice,
      'discount_amount': discountAmount,
      'promo_discount': promoDiscount,
      'total_price': totalPrice,
      'currency': currency,
    };
  }
}

// Mock payment card data (non-functional)
class MockPaymentCard {
  final String nameOnCard;
  final String cardNumber;
  final String expiryDate;
  final String cvv;

  const MockPaymentCard({
    required this.nameOnCard,
    required this.cardNumber,
    required this.expiryDate,
    required this.cvv,
  });

  bool get isValid {
    // DEVELOPMENT MODE: Always return true
    return true;

    // TODO: Re-enable for production
    // return nameOnCard.isNotEmpty &&
    //        cardNumber.length >= 16 &&
    //        expiryDate.length >= 5 &&
    //        cvv.length >= 3;
  }

  MockPaymentCard copyWith({
    String? nameOnCard,
    String? cardNumber,
    String? expiryDate,
    String? cvv,
  }) {
    return MockPaymentCard(
      nameOnCard: nameOnCard ?? this.nameOnCard,
      cardNumber: cardNumber ?? this.cardNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      cvv: cvv ?? this.cvv,
    );
  }
}

// Payment state
class PaymentState {
  final OrderSummary? orderSummary;
  final MockPaymentCard paymentCard;
  final bool isProcessing;
  final bool isCompleted;
  final String? error;
  final PaymentTheme theme;

  const PaymentState({
    this.orderSummary,
    this.paymentCard = const MockPaymentCard(
      nameOnCard: 'Test Kullanıcı',
      cardNumber: '4111 1111 1111 1111',
      expiryDate: '12/28',
      cvv: '123',
    ),
    this.isProcessing = false,
    this.isCompleted = false,
    this.error,
    this.theme = PaymentTheme.standard,
  });

  PaymentState copyWith({
    OrderSummary? orderSummary,
    MockPaymentCard? paymentCard,
    bool? isProcessing,
    bool? isCompleted,
    String? error,
    PaymentTheme? theme,
    bool clearError = false,
  }) {
    return PaymentState(
      orderSummary: orderSummary ?? this.orderSummary,
      paymentCard: paymentCard ?? this.paymentCard,
      isProcessing: isProcessing ?? this.isProcessing,
      isCompleted: isCompleted ?? this.isCompleted,
      error: clearError ? null : (error ?? this.error),
      theme: theme ?? this.theme,
    );
  }

  bool get canProceed {
    // DEVELOPMENT MODE: Always allow proceed if order exists
    return orderSummary != null && !isProcessing;

    // TODO: Re-enable for production
    // return orderSummary != null &&
    //        paymentCard.isValid &&
    //        !isProcessing &&
    //        !isCompleted;
  }
}

// Payment theme (matches enrollment theme)
enum PaymentTheme {
  standard,
  premium;

  bool get isPremium => this == PaymentTheme.premium;
}

// Payment result
class PaymentResult {
  final bool isSuccess;
  final String? transactionId;
  final String? errorMessage;
  final DateTime timestamp;

  const PaymentResult({
    required this.isSuccess,
    this.transactionId,
    this.errorMessage,
    required this.timestamp,
  });

  factory PaymentResult.success(String transactionId) {
    return PaymentResult(
      isSuccess: true,
      transactionId: transactionId,
      timestamp: DateTime.now(),
    );
  }

  factory PaymentResult.failure(String errorMessage) {
    return PaymentResult(
      isSuccess: false,
      errorMessage: errorMessage,
      timestamp: DateTime.now(),
    );
  }
}
