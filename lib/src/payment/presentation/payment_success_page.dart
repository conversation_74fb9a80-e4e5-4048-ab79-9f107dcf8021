import 'package:flutter/material.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/payment/domain/payment_models.dart';
import 'package:fitgo_app/src/profile_form/presentation/profile_form_screen.dart';

class PaymentSuccessPage extends StatelessWidget {
  final OrderSummary orderSummary;
  final String transactionId;
  final PaymentTheme theme;

  const PaymentSuccessPage({
    super.key,
    required this.orderSummary,
    required this.transactionId,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: _getBackgroundColor(),
        appBar: AppBar(
          backgroundColor: _getBackgroundColor(),
          elevation: 0,
          leading: const SizedBox(),
          title: TextWidget(
            'Kayı<PERSON> Tamamlandı'.hardcoded,
            style: ATextStyle.large.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Üst içerik, taşma olursa kaydırılabilir
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Success animation/icon
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: _getAccentColor().withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.check_circle,
                            color: _getAccentColor(),
                            size: 80,
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Success message
                        TextWidget(
                          'Tebrikler!'.hardcoded,
                          style: ATextStyle.large.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 28,
                          ),
                        ),
                        const SizedBox(height: 16),

                        TextWidget(
                          '${orderSummary.trainerName} eğitmenine başarıyla kayıt oldunuz.'
                              .hardcoded,
                          style: ATextStyle.medium.copyWith(
                            color: Colors.white70,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 32),

                        // Order details card
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: _getCardBackgroundColor(),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: _getBorderColor(),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              _buildDetailRow(
                                'Eğitmen',
                                orderSummary.trainerName,
                              ),
                              const SizedBox(height: 12),
                              _buildDetailRow(
                                'Paket',
                                orderSummary.packageName,
                              ),
                              const SizedBox(height: 12),
                              _buildDetailRow('Süre', orderSummary.duration),
                              const SizedBox(height: 12),
                              _buildDetailRow(
                                'Toplam',
                                '${orderSummary.totalPrice.toStringAsFixed(0)}${orderSummary.currency}',
                              ),
                              const SizedBox(height: 16),
                              Divider(color: Colors.white24),
                              const SizedBox(height: 16),
                              _buildDetailRow(
                                'İşlem No',
                                transactionId,
                                isTransactionId: true,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Demo notice
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.orange.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.orange.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Colors.orange,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: TextWidget(
                                  'Bu bir demo işlemdir. Gerçek ödeme alınmamıştır.'
                                      .hardcoded,
                                  style: ATextStyle.small.copyWith(
                                    color: Colors.orange,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Alt buton
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pushReplacement(
                          MaterialPageRoute(
                            builder: (context) => const ProfileFormScreen(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _getAccentColor(),
                        foregroundColor:
                            theme == PaymentTheme.premium
                                ? Colors.black
                                : Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: TextWidget(
                        'Devam Et'.hardcoded,
                        style: ATextStyle.medium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    bool isTransactionId = false,
  }) {
    return Row(
      children: [
        Expanded(
          child: TextWidget(
            label,
            style: ATextStyle.medium.copyWith(color: Colors.white70),
          ),
        ),
        Flexible(
          child: TextWidget(
            value,
            style: ATextStyle.medium.copyWith(
              color: isTransactionId ? _getAccentColor() : Colors.white,
              fontWeight: FontWeight.w600,
              fontFamily: isTransactionId ? 'monospace' : null,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Color _getBackgroundColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF0F172A);
      case PaymentTheme.premium:
        return const Color(0xFF1A1A2E);
    }
  }

  Color _getCardBackgroundColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF1E293B);
      case PaymentTheme.premium:
        return const Color(0xFF2D1B69).withOpacity(0.3);
    }
  }

  Color _getBorderColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return const Color(0xFF334155);
      case PaymentTheme.premium:
        return const Color(0xFFFFD700).withOpacity(0.3);
    }
  }

  Color _getAccentColor() {
    switch (theme) {
      case PaymentTheme.standard:
        return AColor.fitgoGreen;
      case PaymentTheme.premium:
        return const Color(0xFFFFD700);
    }
  }
}
