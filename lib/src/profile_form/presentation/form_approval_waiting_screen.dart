import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';

class FormApprovalWaitingScreen extends HookConsumerWidget {
  const FormApprovalWaitingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Animation controller for the success icon
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 1500),
    );
    
    final scaleAnimation = useAnimation(
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: animationController,
          curve: Curves.elasticOut,
        ),
      ),
    );

    // Start animation when screen loads
    useEffect(() {
      animationController.forward();
      return null;
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Header with back button
              Row(
                children: [
                  IconButton(
                    onPressed: () => context.go('/dashboard'),
                    icon: const Icon(
                      Icons.arrow_back,
                      color: AColor.textColor,
                      size: 24,
                    ),
                  ),
                  Expanded(
                    child: TextWidget(
                      'Form Durumu'.hardcoded,
                      style: ATextStyle.large.copyWith(
                        color: AColor.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 48), // Balance the back button
                ],
              ),

              const SizedBox(height: 60),

              // Success animation and content
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated success icon
                    Transform.scale(
                      scale: scaleAnimation,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: AColor.fitgoGreen.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AColor.fitgoGreen,
                            width: 3,
                          ),
                        ),
                        child: const Icon(
                          Icons.check_circle,
                          color: AColor.fitgoGreen,
                          size: 60,
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Success title
                    TextWidget(
                      'Formunuz Başarıyla Gönderildi!'.hardcoded,
                      style: ATextStyle.semiLarge.copyWith(
                        color: AColor.textColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // Description
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: TextWidget(
                        'Formunuz başarılı bir şekilde eğitmeninize gönderildi. Eğitmeninizin formu onaylamasını bekleyiniz.'.hardcoded,
                        style: ATextStyle.medium.copyWith(
                          color: AColor.grey,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Status card
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1F2937),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AColor.fitgoGreen.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: Colors.orange,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 12),
                              TextWidget(
                                'Durum: Onay Bekleniyor'.hardcoded,
                                style: ATextStyle.medium.copyWith(
                                  color: AColor.textColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Icon(
                                Icons.schedule,
                                color: AColor.grey,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              TextWidget(
                                'Ortalama onay süresi: 24-48 saat'.hardcoded,
                                style: ATextStyle.small.copyWith(
                                  color: AColor.grey,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Next steps info
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1F2937),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextWidget(
                            'Sonraki Adımlar:'.hardcoded,
                            style: ATextStyle.medium.copyWith(
                              color: AColor.textColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _buildNextStepItem(
                            '1. Eğitmeniniz formunuzu inceleyecek',
                            Icons.visibility,
                          ),
                          const SizedBox(height: 8),
                          _buildNextStepItem(
                            '2. Uygun program önerileri hazırlanacak',
                            Icons.fitness_center,
                          ),
                          const SizedBox(height: 8),
                          _buildNextStepItem(
                            '3. Size bildirim gönderilecek',
                            Icons.notifications,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Bottom button
              Container(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => context.go('/dashboard'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AColor.fitgoGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 4,
                    shadowColor: AColor.fitgoGreen.withValues(alpha: 0.3),
                  ),
                  child: TextWidget(
                    'Ana Sayfaya Dön'.hardcoded,
                    style: ATextStyle.large.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNextStepItem(String text, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: AColor.fitgoGreen,
          size: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextWidget(
            text,
            style: ATextStyle.small.copyWith(
              color: AColor.grey,
            ),
          ),
        ),
      ],
    );
  }
}
