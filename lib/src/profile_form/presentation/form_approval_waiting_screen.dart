import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/dashboard/application/dashboard_provider.dart';

class FormApprovalWaitingScreen extends HookConsumerWidget {
  const FormApprovalWaitingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 1500),
    );
    final scaleAnimation = useAnimation(
      Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: animationController, curve: Curves.elasticOut),
      ),
    );
    useEffect(() {
      animationController.forward();
      return null;
    }, []);

    return Scaffold(
      backgroundColor: const Color(0xFF111827),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Header
              Row(
                children: [
                  // Geri butonu kaldırıldı - öğrenci bu ekranda kalmalı
                  Expanded(
                    child: TextWidget(
                      'Plan Bekleniyor'.hardcoded,
                      style: ATextStyle.large.copyWith(
                        color: AColor.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 48),
                ],
              ),

              const SizedBox(height: 24),

              // İçerik (kaydırılabilir)
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 36),
                      Transform.scale(
                        scale: scaleAnimation,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: AColor.fitgoGreen.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AColor.fitgoGreen,
                              width: 3,
                            ),
                          ),
                          child: const Icon(
                            Icons.schedule,
                            color: AColor.fitgoGreen,
                            size: 60,
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),
                      TextWidget(
                        'Antrenman Planınız Hazırlanıyor'.hardcoded,
                        style: ATextStyle.semiLarge.copyWith(
                          color: AColor.textColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 24,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: TextWidget(
                          'Eğitmeniniz sizin için özel bir antrenman planı hazırlıyor. Plan hazır olduğunda bildirim alacaksınız.'
                              .hardcoded,
                          style: ATextStyle.medium.copyWith(
                            color: AColor.grey,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 40),
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: const Color(0xFF1F2937),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: AColor.fitgoGreen.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: Colors.orange,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                TextWidget(
                                  'Durum: Onay Bekleniyor'.hardcoded,
                                  style: ATextStyle.medium.copyWith(
                                    color: AColor.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Icon(
                                  Icons.schedule,
                                  color: AColor.grey,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                TextWidget(
                                  'Ortalama onay süresi: 24-48 saat'.hardcoded,
                                  style: ATextStyle.small.copyWith(
                                    color: AColor.grey,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 40),
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFF1F2937),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              'Sonraki Adımlar:'.hardcoded,
                              style: ATextStyle.medium.copyWith(
                                color: AColor.textColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            _buildNextStepItem(
                              '1. Eğitmeniniz formunuzu inceleyecek',
                              Icons.visibility,
                            ),
                            const SizedBox(height: 8),
                            _buildNextStepItem(
                              '2. Uygun program önerileri hazırlanacak',
                              Icons.fitness_center,
                            ),
                            const SizedBox(height: 8),
                            _buildNextStepItem(
                              '3. Size bildirim gönderilecek',
                              Icons.notifications,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),

              // Alt butonlar
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Column(
                  children: [
                    // Satın alımları görme butonu
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {
                          // TODO: Satın alımlar sayfasına yönlendir
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Satın alımlar sayfası yakında eklenecek',
                              ),
                              backgroundColor: AColor.fitgoGreen,
                            ),
                          );
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AColor.fitgoGreen,
                          side: const BorderSide(
                            color: AColor.fitgoGreen,
                            width: 2,
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.receipt_long, size: 20),
                            const SizedBox(width: 8),
                            TextWidget(
                              'Satın Alımlarım'.hardcoded,
                              style: ATextStyle.large.copyWith(
                                color: AColor.fitgoGreen,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Yenile butonu
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () async {
                          // Plan durumunu kontrol et
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Plan durumu kontrol ediliyor...'),
                              backgroundColor: AColor.fitgoGreen,
                            ),
                          );

                          // Dashboard repository'den plan durumunu kontrol et
                          final dashboardRepository = ref.read(
                            dashboardRepositoryProvider,
                          );
                          final onboardingStatus =
                              await dashboardRepository.getOnboardingStatus();

                          if (!context.mounted) return;

                          if (onboardingStatus.shouldShowDashboard) {
                            // Plan atanmış, dashboard'a yönlendir
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Planınız hazır! Yönlendiriliyorsunuz...',
                                ),
                                backgroundColor: AColor.fitgoGreen,
                              ),
                            );
                            context.go('/dashboard');
                          } else {
                            // Henüz plan atanmamış
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Planınız henüz hazır değil. Lütfen daha sonra tekrar kontrol edin.',
                                ),
                                backgroundColor: Colors.orange,
                              ),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AColor.fitgoGreen,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 18),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 4,
                          shadowColor: AColor.fitgoGreen.withValues(alpha: 0.3),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.refresh, size: 20),
                            const SizedBox(width: 8),
                            TextWidget(
                              'Durumu Kontrol Et'.hardcoded,
                              style: ATextStyle.large.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNextStepItem(String text, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: AColor.fitgoGreen, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: TextWidget(
            text,
            style: ATextStyle.small.copyWith(color: AColor.grey),
          ),
        ),
      ],
    );
  }
}
