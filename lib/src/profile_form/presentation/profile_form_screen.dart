import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/dashboard/application/dashboard_provider.dart';
import 'package:fitgo_app/src/profile_form/application/profile_provider.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/profile_form/domain/profile_models.dart';
import 'package:fitgo_app/src/profile_form/application/profile_provider.dart';
import 'package:fitgo_app/src/profile_form/presentation/components/body_photos_section.dart';
import 'package:fitgo_app/src/profile_form/presentation/components/fitness_goals_field.dart';
import 'package:fitgo_app/src/profile_form/presentation/components/activity_level_selector.dart';
import 'package:fitgo_app/src/profile_form/presentation/components/dietary_restrictions_section.dart';
import 'package:fitgo_app/src/profile_form/presentation/components/text_fields_section.dart';

import 'package:fitgo_app/src/dashboard/presentation/dashboard_screen.dart';

class ProfileFormScreen extends HookConsumerWidget {
  const ProfileFormScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileState = ref.watch(profileFormProvider);
    final scrollController = useScrollController();

    // Initialize form when screen loads
    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(profileFormProvider.notifier).initializeForm();
      });
      return null;
    }, []);

    // Note: Navigation is now handled directly in submit button onPressed
    // to ensure we always navigate in development mode

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        elevation: 0,
        leading: const SizedBox(), // Remove back button for onboarding flow
        title: Row(
          children: [
            Icon(
              Icons.person_add,
              color: AColor.fitgoGreen,
              size: 24,
            ),
            const SizedBox(width: 8),
            TextWidget(
              'Kendinden Bahset'.hardcoded,
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome message
                  _buildWelcomeMessage(),
                  const SizedBox(height: 24),
                  
                  // Body photos section
                  BodyPhotosSection(
                    photos: profileState.form.bodyPhotos,
                    isUploading: profileState.isUploadingPhotos,
                    uploadProgress: profileState.uploadProgress,
                    onPhotoTap: (photoType) => _showPhotoPickerModal(context, ref, photoType),
                    onPhotoRemove: (photoType) {
                      ref.read(profileFormProvider.notifier).removeBodyPhoto(photoType);
                    },
                  ),
                  const SizedBox(height: 32),
                  
                  // Fitness goals
                  FitnessGoalsField(
                    value: profileState.form.fitnessGoals,
                    onChanged: (value) {
                      ref.read(profileFormProvider.notifier).updateFitnessGoals(value);
                    },
                  ),
                  const SizedBox(height: 24),
                  
                  // Activity level
                  ActivityLevelSelector(
                    selectedLevel: profileState.form.activityLevel,
                    onChanged: (level) {
                      ref.read(profileFormProvider.notifier).updateActivityLevel(level);
                    },
                  ),
                  const SizedBox(height: 24),
                  
                  // Dietary restrictions
                  DietaryRestrictionsSection(
                    selectedRestrictions: profileState.form.dietaryRestrictions,
                    onToggle: (restriction) {
                      ref.read(profileFormProvider.notifier).toggleDietaryRestriction(restriction);
                    },
                  ),
                  const SizedBox(height: 24),
                  
                  // Text fields (medical conditions, additional notes)
                  TextFieldsSection(
                    medicalConditions: profileState.form.medicalConditions,
                    additionalNotes: profileState.form.additionalNotes,
                    onMedicalConditionsChanged: (value) {
                      ref.read(profileFormProvider.notifier).updateMedicalConditions(value);
                    },
                    onAdditionalNotesChanged: (value) {
                      ref.read(profileFormProvider.notifier).updateAdditionalNotes(value);
                    },
                  ),
                  const SizedBox(height: 24),

                  // Weight and Height section
                  _buildWeightHeightSection(context, ref, profileState),
                  
                  // Error message
                  if (profileState.error != null) ...[
                    const SizedBox(height: 16),
                    _buildErrorMessage(profileState.error!),
                  ],
                  
                  const SizedBox(height: 100), // Space for submit button
                ],
              ),
            ),
          ),
          
          // Submit button
          Container(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: profileState.canSubmit ? () async {
                  try {
                    // Show loading state
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text('Form gönderiliyor...'),
                          ],
                        ),
                        backgroundColor: AColor.fitgoGreen,
                        duration: Duration(seconds: 30), // Long duration for submission
                      ),
                    );

                    // Submit the profile form
                    final result = await ref.read(profileFormProvider.notifier).submitProfile();

                    // Hide loading snackbar
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    }

                    if (result.isSuccess) {
                      // Mark profile form as completed so user won't see it again
                      await ref.read(onboardingNotifierProvider.notifier).markProfileFormCompleted();

                      // Navigate to approval waiting screen
                      if (context.mounted) {
                        context.go('/form-approval-waiting');
                      }
                    } else {
                      // Show error message
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(result.errorMessage ?? 'Form gönderilirken hata oluştu'),
                            backgroundColor: Colors.red,
                            duration: Duration(seconds: 5),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    // Hide loading snackbar
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    }

                    // Show error message
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Beklenmeyen hata: ${e.toString()}'),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 5),
                        ),
                      );
                    }
                  }
                } : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: profileState.canSubmit ? AColor.fitgoGreen : Colors.grey[600],
                  foregroundColor: Colors.white,
                  disabledBackgroundColor: Colors.grey[600],
                  disabledForegroundColor: Colors.grey[400],
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: profileState.canSubmit ? 4 : 0,
                  shadowColor: AColor.fitgoGreen.withValues(alpha: 0.3),
                ),
                child: TextWidget(
                  'Gönder'.hardcoded,
                  style: ATextStyle.large.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildWelcomeMessage() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AColor.fitgoGreen.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.waving_hand,
                color: AColor.fitgoGreen,
                size: 24,
              ),
              const SizedBox(width: 8),
              TextWidget(
                'Hoş Geldin!'.hardcoded,
                style: ATextStyle.large.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextWidget(
            'Eğitmenin sana en uygun programı hazırlayabilmesi için kendinden bahset. Bu bilgiler sadece eğitmeninle paylaşılacak.'.hardcoded,
            style: ATextStyle.medium.copyWith(
              color: Colors.white70,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeightHeightSection(BuildContext context, WidgetRef ref, ProfileFormState profileState) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1E293B),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AColor.fitgoGreen.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.monitor_weight,
                color: AColor.fitgoGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              TextWidget(
                'Fiziksel Bilgiler'.hardcoded,
                style: ATextStyle.medium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              // Weight field
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      'Kilo (kg)'.hardcoded,
                      style: ATextStyle.small.copyWith(
                        color: Colors.grey[300],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      style: ATextStyle.medium.copyWith(color: Colors.white),
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      decoration: InputDecoration(
                        hintText: 'Örn: 70.5',
                        hintStyle: ATextStyle.medium.copyWith(color: Colors.grey[500]),
                        filled: true,
                        fillColor: const Color(0xFF334155),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AColor.fitgoGreen, width: 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      onChanged: (value) {
                        final weight = double.tryParse(value);
                        ref.read(profileFormProvider.notifier).updateWeight(weight);
                      },
                      controller: TextEditingController(
                        text: profileState.form.weight?.toString() ?? '',
                      )..selection = TextSelection.fromPosition(
                        TextPosition(offset: profileState.form.weight?.toString().length ?? 0),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // Height field
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      'Boy (cm)'.hardcoded,
                      style: ATextStyle.small.copyWith(
                        color: Colors.grey[300],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      style: ATextStyle.medium.copyWith(color: Colors.white),
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      decoration: InputDecoration(
                        hintText: 'Örn: 175',
                        hintStyle: ATextStyle.medium.copyWith(color: Colors.grey[500]),
                        filled: true,
                        fillColor: const Color(0xFF334155),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: AColor.fitgoGreen, width: 2),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      onChanged: (value) {
                        final height = double.tryParse(value);
                        ref.read(profileFormProvider.notifier).updateHeight(height);
                      },
                      controller: TextEditingController(
                        text: profileState.form.height?.toString() ?? '',
                      )..selection = TextSelection.fromPosition(
                        TextPosition(offset: profileState.form.height?.toString().length ?? 0),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextWidget(
              error,
              style: ATextStyle.small.copyWith(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showPhotoPickerModal(BuildContext context, WidgetRef ref, BodyPhotoType photoType) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF1E293B),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextWidget(
              '${photoType.displayName} Fotoğrafı Ekle'.hardcoded,
              style: ATextStyle.large.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            
            Row(
              children: [
                Expanded(
                  child: _buildPhotoOption(
                    context: context,
                    icon: Icons.camera_alt,
                    label: 'Kamera'.hardcoded,
                    onTap: () {
                      Navigator.pop(context);
                      ref.read(profileFormProvider.notifier).pickImageFromCamera(photoType);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildPhotoOption(
                    context: context,
                    icon: Icons.photo_library,
                    label: 'Galeri'.hardcoded,
                    onTap: () {
                      Navigator.pop(context);
                      ref.read(profileFormProvider.notifier).pickImageFromGallery(photoType);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: TextWidget(
                'İptal'.hardcoded,
                style: ATextStyle.medium.copyWith(color: Colors.white70),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoOption({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFF334155),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AColor.fitgoGreen.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: AColor.fitgoGreen,
              size: 32,
            ),
            const SizedBox(height: 8),
            TextWidget(
              label,
              style: ATextStyle.medium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
