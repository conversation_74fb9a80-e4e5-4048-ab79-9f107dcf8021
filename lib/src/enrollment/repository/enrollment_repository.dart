import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import '../domain/enrollment_models.dart';

/// Abstract interface for enrollment repository
abstract interface class IEnrollmentRepository {
  Future<EnrollmentCheckResult> checkUserEnrollment(String userId);
  Future<UserRoleInfo?> getUserRole(String userId);
  Future<bool> isUserInstructor(String userId);
}

/// Concrete implementation of enrollment repository using Supabase
class EnrollmentRepository implements IEnrollmentRepository {
  EnrollmentRepository(this._supabase);

  final SupabaseClient _supabase;

  @override
  Future<EnrollmentCheckResult> checkUserEnrollment(String userId) async {
    try {
      debugPrint('🔍 Checking enrollment for user: $userId');
      
      final response = await _supabase
          .from('user_enrollments')
          .select('id, user_id, instructor_id, is_active, enrolled_at, expires_at, progress_percentage, created_at, updated_at')
          .eq('user_id', userId)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) {
        debugPrint('❌ No active enrollment found for user: $userId');
        return const EnrollmentCheckResult(
          hasActiveEnrollment: false,
        );
      }

      final enrollment = UserEnrollment.fromJson({
        'id': response['id'],
        'userId': response['user_id'],
        'instructorId': response['instructor_id'],
        'isActive': response['is_active'],
        'enrolledAt': DateTime.parse(response['enrolled_at']),
        'expiresAt': response['expires_at'] != null 
            ? DateTime.parse(response['expires_at']) 
            : null,
        'progressPercentage': (response['progress_percentage'] ?? 0.0).toDouble(),
        'createdAt': response['created_at'] != null 
            ? DateTime.parse(response['created_at']) 
            : null,
        'updatedAt': response['updated_at'] != null 
            ? DateTime.parse(response['updated_at']) 
            : null,
      });

      debugPrint('✅ Active enrollment found for user: $userId');
      return EnrollmentCheckResult(
        hasActiveEnrollment: true,
        enrollment: enrollment,
      );
    } catch (e) {
      debugPrint('❌ Error checking enrollment for user $userId: $e');
      return EnrollmentCheckResult(
        hasActiveEnrollment: false,
        error: e.toString(),
      );
    }
  }

  @override
  Future<UserRoleInfo?> getUserRole(String userId) async {
    try {
      debugPrint('🔍 Checking user role for: $userId');
      
      // First check profiles table for role
      final profileResponse = await _supabase
          .from('profiles')
          .select('id, role, name, email')
          .eq('id', userId)
          .maybeSingle();

      if (profileResponse != null) {
        debugPrint('✅ Found user role in profiles: ${profileResponse['role']}');
        return UserRoleInfo.fromJson({
          'userId': profileResponse['id'],
          'role': profileResponse['role'],
          'name': profileResponse['name'],
          'email': profileResponse['email'],
        });
      }

      debugPrint('❌ No user role found in profiles for: $userId');
      return null;
    } catch (e) {
      debugPrint('❌ Error getting user role for $userId: $e');
      return null;
    }
  }

  @override
  Future<bool> isUserInstructor(String userId) async {
    try {
      debugPrint('🔍 Checking if user is instructor: $userId');
      
      // First check profiles table
      final userRole = await getUserRole(userId);
      if (userRole != null) {
        final isInstructor = userRole.role == 'instructor';
        debugPrint('✅ User role check complete: ${isInstructor ? 'INSTRUCTOR' : 'STUDENT'}');
        return isInstructor;
      }

      // Fallback: check instructors table directly
      final instructorResponse = await _supabase
          .from('instructors')
          .select('id')
          .eq('id', userId)
          .maybeSingle();

      final isInstructor = instructorResponse != null;
      debugPrint('✅ Instructor table check: ${isInstructor ? 'INSTRUCTOR' : 'STUDENT'}');
      return isInstructor;
    } catch (e) {
      debugPrint('❌ Error checking instructor status for $userId: $e');
      return false;
    }
  }
}
