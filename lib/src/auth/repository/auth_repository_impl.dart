import 'dart:io';
import 'dart:typed_data';

import 'package:fitgo_app/src/auth/domain/profile.dart';
import 'package:fitgo_app/src/auth/domain/user_exception.dart';
import 'package:fitgo_app/src/shared/extensions/map_extensions.dart';
import 'package:fitgo_app/src/shared/logger/debug_logger.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/foundation.dart';

abstract class AuthRepository {
  Future<Profile> registerUser({
    required Profile user,
    required String password,
    String? firebaseToken,
  });

  Future<Profile> signInWithEmail({
    required String email,
    required String password,
  });

  Future<Profile> currentUser();

  Future<Profile> getUserById(String id);

  Future<void> updateUser(Profile user);

  Future<void> signOut();

  Future<void> updateEmail(String userId, String newEmail, String password);

  Future<void> updatePassword(String userId, String newPassword);

  Future<bool> validatePassword(String userId, String password);

  Future<void> updateUserToken(String token);

  Future<void> resetPassword(String newPassword, String oobCode);

  Future<bool> verifyResetCode(String code);

  Future<void> signOutMember(String childUID);

  Future<Profile?> getUserByEmail(String email);

  Future<void> sendPasswordResetEmail(String email);

  Future<void> updatePasswordWithToken(
    String newPassword,
    String? accessToken,
    String? refreshToken,
  );

  Future<bool> checkUserPhoneExist(String phone);

  Future<String> uploadImage(
    String userId, {
    Uint8List? imageData,
    XFile? file,
  });

  Future<void> addNotificationToken({
    required String userId,
    required String token,
  });
}

class AuthRepositoryImpl implements AuthRepository {
  AuthRepositoryImpl(this._client);

  final SupabaseClient _client;

  @override
  Future<Profile> registerUser({
    required Profile user,
    required String password,
    String? firebaseToken,
  }) async {
    try {
      if (user.phone != null && user.phone!.isNotEmpty) {
        final isPhoneExist = await _client
            .from('profiles')
            .select()
            .eq('phone', user.phone!);
        if (isPhoneExist.isNotEmpty) {
          throw UserException(type: UserExceptionType.phoneExist);
        }
      }

      final response = await _client.auth.signUp(
        email: user.email,
        password: password,
        data: {
          'name': user.name,
          'surname': user.surname,
          'phone': user.phone,
          'role': user.role.name,
          'date_of_birth': user.dateOfBirth?.toIso8601String(),
        },
      );

      if (firebaseToken != null && firebaseToken.isNotEmpty) {
        await _client.from('notification_tokens').insert({
          'user_id': response.user!.id,
          'firebase_token': firebaseToken,
        });
      }

      // If user is instructor, create instructor record
      if (user.role == UserType.instructor) {
        await _createInstructorRecord(
          userId: response.user!.id,
          name: user.name ?? 'Unknown',
          surname: user.surname,
          email: user.email,
        );
      }

      return user.copyWith(id: response.user!.id);
    } catch (e) {
      if (e is UserException) {
        rethrow;
      } else if (e is AuthException) {
        if (e.code == 'email_exists') {
          throw UserException(type: UserExceptionType.emailExist);
        } else if (e.code == 'phone_exists') {
          throw UserException(type: UserExceptionType.phoneExist);
        } else if (e.code == 'user_already_exists') {
          throw UserException(type: UserExceptionType.userExist);
        } else if (e.code == 'validation_failed') {
          throw UserException(type: UserExceptionType.validationFailed);
        }
      }
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<void> updateUserToken(String token) async {
    try {
      final user = _client.auth.currentUser;

      await _client.from('notification_tokens').upsert({
        'user_id': user!.id,
        'firebase_token': token,
      });
    } catch (e) {
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<Profile> currentUser() async {
    try {
      final user = _client.auth.currentUser;
      final dbResponse =
          await _client.from('profiles').select().eq('id', user!.id).single();

      return Profile.fromJson(dbResponse);
    } catch (e) {
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<Profile> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      loge('🔐 Attempting login for email: $email');

      final response = await _client.auth.signInWithPassword(
        password: password,
        email: email,
      );

      loge('✅ Supabase auth successful');
      final jwt = response.session?.accessToken;
      loge('JWT Token == == ===>  $jwt');

      if (response.user == null) {
        loge('❌ No user in response');
        throw UserException(type: UserExceptionType.unexpected);
      }

      loge('👤 User ID: ${response.user!.id}');
      loge('📧 User email: ${response.user!.email}');

      final profile = await getUserById(response.user!.id);
      loge(
        '✅ Profile fetched successfully: ${profile.name} ${profile.surname}',
      );

      return profile;
    } catch (e) {
      loge('❌ Login error: $e');
      loge('❌ Error type: ${e.runtimeType}');

      if (e is AuthException) {
        loge('🔍 AuthException details:');
        loge('   - Code: ${e.code}');
        loge('   - Message: ${e.message}');
        loge('   - Status Code: ${e.statusCode}');

        if (e.statusCode != null) {
          if (e.statusCode == '403' ||
              e.statusCode == '429' ||
              e.statusCode == '500') {
            throw UserException(type: UserExceptionType.toManyAttempts);
          }
        }

        if (e.code == 'invalid_credentials') {
          throw UserException(type: UserExceptionType.wrongCredential);
        } else if (e.code == 'user_not_found') {
          throw UserException(type: UserExceptionType.notFound);
        }
        if (e.code == 'validation_failed') {
          throw UserException(type: UserExceptionType.validationFailed);
        }
      }

      loge('❌ Throwing unexpected error');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<Profile> getUserById(String id) async {
    try {
      loge('🔍 Fetching profile for user ID: $id');

      final dbResponse =
          await _client
              .from('profiles')
              .select()
              .eq('id', id)
              .limit(1)
              .single();

      loge('✅ Profile data fetched: $dbResponse');

      final profile = Profile.fromJson(dbResponse);
      loge(
        '✅ Profile parsed: ${profile.name} ${profile.surname} (${profile.role})',
      );

      return profile;
    } catch (e) {
      loge('❌ Error fetching profile: $e');
      loge('❌ Error type: ${e.runtimeType}');
      throw UserException(type: UserExceptionType.notFound);
    }
  }

  @override
  Future<bool> checkUserPhoneExist(String phone) async {
    try {
      final response = await _client.rpc(
        'check_phone_exists',
        params: {'phone_number': phone},
      );

      return response as bool;
    } catch (e) {
      loge('Error checking phone existence: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<Profile?> getUserByEmail(String email) async {
    try {
      final response =
          await _client
              .from('profiles')
              .select()
              .eq('email', email)
              .limit(1)
              .maybeSingle();

      if (response == null) {
        return null;
      }
      return Profile.fromJson(response);
    } catch (e) {
      loge('Error retrieving user by email: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<void> resetPassword(String newPassword, String oobCode) async {
    // TODO: implement resetPassword with Supabase
    throw UnimplementedError('Reset password not implemented yet');
  }

  @override
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<void> updatePasswordWithToken(
    String newPassword,
    String? accessToken,
    String? refreshToken,
  ) async {
    try {
      if (accessToken != null && refreshToken != null) {
        // Set the session with the tokens from the reset link
        await _client.auth.setSession(accessToken);
      }

      // Update the password
      await _client.auth.updateUser(UserAttributes(password: newPassword));

      logi('Password updated successfully');
    } catch (e) {
      loge('Error updating password: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<void> signOut() async {
    try {
      final userId = _client.auth.currentUser?.id;
      await _client.auth.signOut();

      if (userId != null) {
        await _client
            .from('notification_tokens')
            .delete()
            .eq('user_id', userId);
      }

      logi('User signed out successfully');
    } catch (e) {
      loge('Error signing out: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<void> signOutMember(String childUID) async {
    try {
      final response =
          await _client
              .from('profiles')
              .update({'firebase_token': null})
              .eq('id', childUID)
              .select()
              .single();

      if (response.isEmpty) {
        throw UserException(type: UserExceptionType.notFound);
      }

      logi('Child member signed out successfully: $childUID');
    } catch (e) {
      loge('Error signing out member: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<void> updateEmail(
    String userId,
    String newEmail,
    String password,
  ) async {
    try {
      final currentUser = await getUserById(userId);

      // Verify current password by signing in
      await _client.auth.signInWithPassword(
        email: currentUser.email,
        password: password,
      );

      // Update email in auth
      await _client.auth.updateUser(UserAttributes(email: newEmail));

      // Update email in profiles table
      await _client
          .from('profiles')
          .update({'email': newEmail})
          .eq('id', userId);

      logi('Email updated successfully for user ID: $userId');
    } on AuthException catch (e) {
      loge('Error during email update: ${e.message}');
      if (e.code == 'validation_failed') {
        throw UserException(type: UserExceptionType.validationFailed);
      }
      throw UserException(type: UserExceptionType.unexpected);
    } catch (e) {
      loge('Unexpected error during email update: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<void> updatePassword(String userId, String newPassword) async {
    try {
      await _client.auth.updateUser(UserAttributes(password: newPassword));
      logi('Password updated successfully for user ID: $userId');
    } catch (e) {
      loge('Error updating password: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<bool> validatePassword(String userId, String password) async {
    try {
      final user = await getUserById(userId);
      final response = await _client.auth.signInWithPassword(
        email: user.email,
        password: password,
      );
      return response.user != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> verifyResetCode(String code) async {
    // TODO: implement verifyResetCode with Supabase
    throw UnimplementedError('Verify reset code not implemented yet');
  }

  @override
  Future<String> uploadImage(
    String userId, {
    Uint8List? imageData,
    XFile? file,
  }) async {
    final filePath = '$userId.png';
    final storageRef = _client.storage.from('profile_images');

    try {
      await _deleteImage(userId);
      if (imageData != null) {
        await storageRef.uploadBinary(filePath, imageData);
      } else if (file != null) {
        final fileToUpload = File(file.path);
        await storageRef.upload(filePath, fileToUpload);
      }

      return storageRef.getPublicUrl(filePath);
    } catch (e) {
      loge('Error uploading image: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateUser(Profile user) async {
    try {
      final userData = user.toJson();

      final finalUserData = userData.withoutKeys(['id', 'created_at']);

      final response =
          await _client
              .from('profiles')
              .update(finalUserData)
              .eq('id', user.id)
              .select()
              .single();

      if (response.isEmpty) {
        loge('Supabase update response is empty for user ID: ${user.id}');
        throw UserException(type: UserExceptionType.unexpected);
      }

      logi('User updated successfully: ${user.id}');
    } on PostgrestException catch (e) {
      loge('Supabase PostgrestException during updateUser: ${e.message}');
      throw UserException(type: UserExceptionType.unexpected);
    } catch (e) {
      loge('Unexpected error in updateUser: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  @override
  Future<void> addNotificationToken({
    required String userId,
    required String token,
  }) async {
    try {
      await _client.from('notification_tokens').upsert({
        'user_id': userId,
        'firebase_token': token,
      });
    } catch (e) {
      loge('Error adding notification token: $e');
      throw UserException(type: UserExceptionType.unexpected);
    }
  }

  Future<void> _deleteImage(String userId) async {
    final filePath = '$userId.png';
    final storageRef = _client.storage.from('profile_images');

    try {
      await storageRef.remove([filePath]);
    } catch (e) {
      loge('Error deleting image: $e');
      // Don't rethrow here as this is a helper method
    }
  }

  /// Create instructor record in instructors table
  Future<void> _createInstructorRecord({
    required String userId,
    required String name,
    String? surname,
    required String email,
  }) async {
    try {
      debugPrint('🔄 Creating instructor record for user: $userId');
      debugPrint('📝 Name: $name, Email: $email');

      await _client.from('instructors').insert({
        'id': userId,
        'name': name,
        'surname': surname,
        'email': email,
        'title': 'Fitness Trainer', // Default title
        'experience_years': 0, // Default experience
        'is_public': false, // Not public until profile is completed
        'is_available': false, // Not available until approved
        'profile_completed': false, // Profile not completed yet
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      debugPrint('✅ Instructor record created successfully');
    } catch (e) {
      debugPrint('❌ Error creating instructor record: $e');
      // Don't throw error to avoid breaking registration flow
      loge('Error creating instructor record: $e');
    }
  }
}
