import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class ResetPasswordVerifyScreen extends HookConsumerWidget {
  const ResetPasswordVerifyScreen({
    super.key,
    required this.token,
    this.email,
  });

  final String token;
  final String? email;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = useState(true);
    final errorMessage = useState<String?>(null);

    useEffect(() {
      // Verify token and exchange for session
      _verifyToken();
      return null;
    }, []);

    Future<void> _verifyToken() async {
      try {
        isLoading.value = true;
        errorMessage.value = null;

        // Verify the token with Supabase
        final response = await Supabase.instance.client.auth.verifyOTP(
          token: token,
          type: OtpType.recovery,
          email: email,
        );

        if (response.session != null) {
          // Token verified successfully, navigate to reset password
          final accessToken = response.session!.accessToken;
          final refreshToken = response.session!.refreshToken;
          
          context.go('/reset-password?access_token=$accessToken&refresh_token=$refreshToken&email=${email ?? ''}');
        } else {
          errorMessage.value = 'Token verification failed';
        }
      } catch (e) {
        print('Token verification error: $e');
        errorMessage.value = 'Şifre sıfırlama bağlantısının süresi dolmuş veya geçersiz.';
      } finally {
        isLoading.value = false;
      }
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.go('/auth'),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo or Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.lock_reset,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 32),
            
            // Title
            Text(
              'Şifre Sıfırlama',
              style: AppTextStyle.headlineLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            if (isLoading.value) ...[
              // Loading state
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              const SizedBox(height: 16),
              Text(
                'Bağlantı doğrulanıyor...',
                style: AppTextStyle.bodyLarge.copyWith(
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
            ] else if (errorMessage.value != null) ...[
              // Error state
              Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                errorMessage.value!,
                style: AppTextStyle.bodyLarge.copyWith(
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              
              // Retry button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => context.go('/forgot-password'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Yeni Bağlantı İste',
                    style: AppTextStyle.labelLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              // Back to login
              TextButton(
                onPressed: () => context.go('/auth'),
                child: Text(
                  'Giriş Sayfasına Dön',
                  style: AppTextStyle.labelLarge.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
