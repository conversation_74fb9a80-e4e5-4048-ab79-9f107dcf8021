import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class ResetPasswordVerifyScreen extends HookConsumerWidget {
  const ResetPasswordVerifyScreen({super.key, required this.token, this.email});

  final String token;
  final String? email;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = useState(true);
    final errorMessage = useState<String?>(null);

    Future<void> verifyToken() async {
      try {
        isLoading.value = true;
        errorMessage.value = null;

        // Verify the token with Supabase
        final response = await Supabase.instance.client.auth.verifyOTP(
          token: token,
          type: OtpType.recovery,
          email: email,
        );

        if (response.session != null) {
          // Token verified successfully, navigate to reset password
          final accessToken = response.session!.accessToken;
          final refreshToken = response.session!.refreshToken;

          if (context.mounted) {
            context.go(
              '/reset-password?access_token=$accessToken&refresh_token=$refreshToken&email=${email ?? ''}'
                  .hardcoded,
            );
          }
        } else {
          errorMessage.value = 'Token verification failed'.hardcoded;
        }
      } catch (e) {
        print('Token verification error: $e'.hardcoded);
        errorMessage.value =
            'Şifre sıfırlama bağlantısının süresi dolmuş veya geçersiz.'
                .hardcoded;
      } finally {
        isLoading.value = false;
      }
    }

    useEffect(() {
      // Verify token and exchange for session
      WidgetsBinding.instance.addPostFrameCallback((_) {
        verifyToken();
      });
      return null;
    }, []);

    return Scaffold(
      backgroundColor: AColor.onboardingBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => context.go('/auth'),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo or Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AColor.fitgoGreen,
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.lock_reset,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 32),

            // Title
            Text(
              'Şifre Sıfırlama'.hardcoded,
              style: ATextStyle.title.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            if (isLoading.value) ...[
              // Loading state
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AColor.fitgoGreen),
              ),
              const SizedBox(height: 16),
              Text(
                'Bağlantı doğrulanıyor...'.hardcoded,
                style: ATextStyle.description.copyWith(color: Colors.white70),
                textAlign: TextAlign.center,
              ),
            ] else if (errorMessage.value != null) ...[
              // Error state
              Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text(
                errorMessage.value!,
                style: ATextStyle.description.copyWith(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Retry button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => context.go('/forgot-password'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AColor.fitgoGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Yeni Bağlantı İste'.hardcoded,
                    style: ATextStyle.buttonText.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Back to login
              TextButton(
                onPressed: () => context.go('/auth'),
                child: Text(
                  'Giriş Sayfasına Dön'.hardcoded,
                  style: ATextStyle.buttonText.copyWith(
                    color: AColor.fitgoGreen,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
