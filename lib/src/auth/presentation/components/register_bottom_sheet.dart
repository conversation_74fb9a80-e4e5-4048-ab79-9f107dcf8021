part of '../auth_page_view.dart';

class RegisterBottomSheet extends HookConsumerWidget {
  const RegisterBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nameCtrl = useTextEditingController();
    final emailCtrl = useTextEditingController();
    final phoneCtrl = useTextEditingController();
    final birthCtrl = useTextEditingController();
    final passwordCtrl = useTextEditingController();
    final confirmCtrl = useTextEditingController();
    final isLoading = useState(false);
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final userType = useState<UserType>(
      ref.read(currentUserTypeProvider) ?? UserType.student,
    );
    final acceptedTerms = useState(false);
    final selectedCountryCode = useState<CountryCode>(
      CountryCode.fromCountryCode('TR'), // Default to Turkey
    );
    final authRepo = ref.read(authRepositoryProvider);

    Future<void> submit() async {
      if (!(formKey.currentState?.validate() ?? false)) return;
      if (passwordCtrl.text != confirmCtrl.text) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Passwords do not match'.hardcoded)),
        );
        return;
      }
      if (!acceptedTerms.value) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Please accept the terms and conditions'.hardcoded),
          ),
        );
        return;
      }
      isLoading.value = true;
      try {
        // Split name into name and surname
        final fullName = nameCtrl.text.trim();
        final nameParts = fullName.split(' ');
        final firstName = nameParts.isNotEmpty ? nameParts.first : '';
        final lastName =
            nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

        // Parse birth date from DD/MM/YYYY format
        DateTime? birthDate;
        if (birthCtrl.text.trim().isNotEmpty) {
          birthDate = DTUtil.parseUserInputDate(birthCtrl.text.trim());
        }

        // Create a Profile object with the user data
        final fullPhoneNumber =
            phoneCtrl.text.trim().isEmpty
                ? null
                : '${selectedCountryCode.value.dialCode}${phoneCtrl.text.trim()}';

        final profile = Profile(
          id: '', // Will be set by the repository
          email: emailCtrl.text.trim(),
          name: firstName,
          surname: lastName,
          phone: fullPhoneNumber,
          dateOfBirth: birthDate,
          role: userType.value,
        );

        await authRepo.registerUser(
          user: profile,
          password: passwordCtrl.text.trim(),
        );

        // Update the global user type provider
        ref.read(currentUserTypeProvider.notifier).state = userType.value;

        if (context.mounted) {
          Navigator.of(context).pop();

          // Navigate based on user type
          if (userType.value == UserType.instructor) {
            // Instructor goes directly to instructor homepage
            context.go('/instructor-main');
          } else {
            // Student goes to course list first (not enrolled in any course)
            context.go('/course-list');
          }
        }
      } catch (err) {
        if (context.mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(err.toString())));
        }
      } finally {
        isLoading.value = false;
      }
    }

    return Padding(
      padding: MediaQuery.of(context).viewInsets,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: AColor.bottomSheetBackgroundColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              spacing: 16,
              children: [
                AppBarBackButton(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                ),
                Container(
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: AColor.white,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () => userType.value = UserType.student,
                          child: Container(
                            decoration: BoxDecoration(
                              color:
                                  userType.value == UserType.student
                                      ? AColor.descriptionColor
                                      : AColor.transparent,
                              borderRadius: BorderRadius.circular(30),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Center(
                              child: TextWidget(
                                'Athlete'.hardcoded,
                                style: ATextStyle.large.copyWith(
                                  color:
                                      userType.value == UserType.instructor
                                          ? AColor.descriptionColor
                                          : Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () => userType.value = UserType.instructor,
                          child: Container(
                            decoration: BoxDecoration(
                              color:
                                  userType.value == UserType.instructor
                                      ? AColor.descriptionColor
                                      : AColor.transparent,
                              borderRadius: BorderRadius.circular(30),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Center(
                              child: TextWidget(
                                'Trainer'.hardcoded,
                                style: ATextStyle.large.copyWith(
                                  color:
                                      userType.value == UserType.student
                                          ? AColor.descriptionColor
                                          : Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                CustomTextFormField(
                  controller: nameCtrl,
                  headerText: 'Name Surname'.hardcoded,
                  keyboardType: TextInputType.name,
                  textInputAction: TextInputAction.next,
                  validator: (value) => value.isValidName(context),
                  regexType: RegexType.name,
                ),
                CustomTextFormField(
                  controller: emailCtrl,
                  headerText: 'E-mail'.hardcoded,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  validator: (value) => value.isValidMail(context),
                  regexType: RegexType.eMail,
                ),
                PhoneInputField(
                  controller: phoneCtrl,
                  headerText: 'Telephone'.hardcoded,
                  textInputAction: TextInputAction.next,
                  validator: (value) => value.isValidPhone(context),
                  onCountryChanged: (countryCode) {
                    selectedCountryCode.value = countryCode;
                  },
                  initialCountryCode: 'TR',
                ),
                CustomTextFormField(
                  controller: birthCtrl,
                  headerText: 'Birth Date (DD/MM/YYYY)'.hardcoded,
                  keyboardType: TextInputType.number,
                  textInputAction: TextInputAction.next,
                  validator: (value) => value.isValidBirthDate(context),
                  regexType: RegexType.birthDate,
                ),
                CustomTextFormField(
                  controller: passwordCtrl,
                  headerText: 'Password'.hardcoded,
                  keyboardType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.next,
                  validator: (value) => value.isValidPassword(context),
                  obscureText: true,
                  regexType: RegexType.password,
                  suffixIcon: const Icon(
                    Icons.visibility_off,
                    color: AColor.grey,
                  ),
                ),
                CustomTextFormField(
                  controller: confirmCtrl,
                  headerText: 'Password Again'.hardcoded,
                  keyboardType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.done,
                  validator:
                      (value) => value.isValidPasswordAgain(
                        context,
                        passwordCtrl.text,
                      ),
                  obscureText: true,
                  regexType: RegexType.password,
                  suffixIcon: const Icon(
                    Icons.visibility_off,
                    color: AColor.grey,
                  ),
                ),
                Row(
                  spacing: 8,
                  children: [
                    GestureDetector(
                      onTap: () => acceptedTerms.value = !acceptedTerms.value,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AColor.white,
                        ),
                        child:
                            acceptedTerms.value
                                ? const Icon(
                                  Icons.check_circle,
                                  size: 23,
                                  color: AColor.descriptionColor,
                                )
                                : null,
                      ),
                    ),
                    Expanded(
                      child: TextWidget(
                        'Privacy Policy ve User Agreement'.hardcoded,
                        style: ATextStyle.medium.copyWith(
                          color: AColor.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
                RaisedButtonWidget(
                  width: double.infinity,
                  text: 'Register'.hardcoded,
                  borderRadius: 15,
                  fontColor: AColor.buttonTextColor,
                  bgColor: AColor.buttonColor,
                  fontStyle: ATextStyle.buttonText,
                  borderSide: BorderSide(
                    color: AColor.textSecondaryColor,
                    width: 2,
                  ),
                  onPressed: isLoading.value ? null : submit,
                ),
                Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextWidget(
                        'Got an account? '.hardcoded,
                        style: ATextStyle.medium.copyWith(
                          color: AColor.white,
                          fontSize: 16,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: TextWidget(
                          'Sign in'.hardcoded,
                          style: ATextStyle.medium.copyWith(
                            color: AColor.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
