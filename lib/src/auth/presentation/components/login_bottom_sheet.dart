part of '../auth_page_view.dart';

class LoginBottomSheet extends HookConsumerWidget {
  const LoginBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailCtrl = useTextEditingController();
    final passwordCtrl = useTextEditingController();
    final isLoading = useState(false);
    final formKey = useMemoized(() => GlobalKey<FormState>());

    void openRegister() {
      ref.read(isRegisterOpenProvider.notifier).state = true;
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: AColor.transparent,
        builder: (_) => const RegisterBottomSheet(),
      ).whenComplete(() {
        ref.read(isRegisterOpenProvider.notifier).state = false;
      });
    }

    Future<void> submit() async {
      if (!(formKey.currentState?.validate() ?? false)) return;

      // Check if widget is still mounted before setting loading state
      if (!context.mounted) return;
      isLoading.value = true;

      try {
        debugPrint('🔐 Starting login process with NEW architecture...');
        debugPrint('📧 Email: ${emailCtrl.text.trim()}');

        // Use ONLY new architecture - AuthController from shared providers
        final authController = ref.read(authControllerProvider);
        debugPrint('🔗 New auth controller obtained');

        final authResponse = await authController.signInWithEmail(
          email: emailCtrl.text.trim(),
          password: passwordCtrl.text.trim(),
        );

        debugPrint('✅ Supabase login successful!');
        debugPrint('👤 User: ${authResponse.user?.email}');

        if (authResponse.user == null) {
          throw Exception('Login failed: No user returned'.hardcoded);
        }

        // Check if widget is still mounted before continuing
        if (!context.mounted) return;

        // Wait for auth state to update
        await Future.delayed(const Duration(milliseconds: 500));

        // Check again after delay
        if (!context.mounted) return;

        // Get user profile from Supabase directly
        final supabaseService = ref.read(supabaseServiceProvider);
        final profileData =
            await supabaseService.client
                .from('profiles')
                .select()
                .eq('id', authResponse.user!.id)
                .single();

        debugPrint('✅ Profile data fetched: $profileData');

        final userRole = UserType.values.firstWhere(
          (role) => role.name == profileData['role'],
          orElse: () => UserType.student,
        );

        debugPrint('👤 User role: $userRole'.hardcoded);

        // Check if widget is still mounted before updating state
        if (!context.mounted) return;

        // Update the global user type provider based on profile role
        await ref.read(currentUserTypeProvider.notifier).setUserType(userRole);

        if (context.mounted) {
          // Pop the bottom sheet
          Navigator.of(context).pop();

          debugPrint('✅ Login successful! Bottom sheet closed.');
          debugPrint('🔄 Auth state will trigger automatic navigation...');

          // Don't navigate here - let the auth state change trigger navigation
          // The splash screen or auth listener will handle the navigation
        }
      } catch (err) {
        debugPrint('❌ Login error: $err');
        debugPrint('❌ Error type: ${err.runtimeType}');
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Login failed: ${err.toString()}')),
          );
        }
      } finally {
        // Only update loading state if widget is still mounted
        if (context.mounted) {
          isLoading.value = false;
        }
      }
    }

    return DraggableScrollableSheet(
      initialChildSize: 0.70,
      minChildSize: 0.3,
      maxChildSize: 0.85,
      expand: false,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: AColor.bottomSheetBackgroundColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: 16,
              bottom: MediaQuery.of(context).viewInsets.bottom + 16,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Drag handle indicator
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: AColor.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Form(
                  key: formKey,
                  child: Column(
                    spacing: 16,
                    children: [
                      CustomTextFormField(
                        controller: emailCtrl,
                        headerText: 'E-mail'.hardcoded,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.next,
                        validator: (value) => value.isValidMail(context),
                        regexType: RegexType.eMail,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          CustomTextFormField(
                            controller: passwordCtrl,
                            headerText: 'Password'.hardcoded,
                            keyboardType: TextInputType.visiblePassword,
                            textInputAction: TextInputAction.done,
                            validator:
                                (value) => value.isValidPassword(context),
                            regexType: RegexType.password,
                            obscureText: true,
                          ),
                          const SizedBox(height: 8),
                          InkWell(
                            onTap: () {
                              // Navigate to forgot password screen
                              context.push('/forgot-password');
                            },
                            child: TextWidget(
                              'Forgot Password?'.hardcoded,
                              style: ATextStyle.medium,
                            ),
                          ),
                        ],
                      ),
                      RaisedButtonWidget(
                        width: double.infinity,
                        text: 'Sign In'.hardcoded,
                        borderRadius: 15,
                        fontColor: AColor.buttonTextColor,
                        bgColor: AColor.buttonColor,
                        fontStyle: ATextStyle.buttonText,
                        borderSide: BorderSide(
                          color: AColor.textSecondaryColor,
                          width: 2,
                        ),
                        onPressed: isLoading.value ? null : submit,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(height: 1, width: 10, color: AColor.white),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: TextWidget('OR'.hardcoded),
                    ),
                    Container(height: 1, width: 10, color: AColor.white),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _SocialButton(icon: Icons.g_mobiledata, onTap: () {}),
                    const SizedBox(width: 16),
                    _SocialButton(icon: Icons.facebook, onTap: () {}),
                    const SizedBox(width: 16),
                    _SocialButton(icon: Icons.one_x_mobiledata, onTap: () {}),
                  ],
                ),
                const SizedBox(height: 16),
                const Divider(color: AColor.textSecondaryColor),
                const SizedBox(height: 16),
                InkWell(
                  onTap: openRegister,
                  child: TextWidget(
                    'No Account? <u>Register Now</u>'.hardcoded,
                    customTag: {
                      'u': StyledTextTag(
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                          decorationColor: AColor.white,
                        ),
                      ),
                    },
                    style: ATextStyle.medium.copyWith(
                      fontSize: 16,
                      fontFamily: AppFonts.poppins,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _SocialButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _SocialButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: CircleAvatar(
        radius: 24,
        backgroundColor: AColor.buttonColor,
        child: Icon(icon, color: AColor.white),
      ),
    );
  }
}
