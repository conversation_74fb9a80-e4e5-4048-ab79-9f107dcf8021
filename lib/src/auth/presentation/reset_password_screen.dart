import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/language_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/app_bar_back_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/theme/colors.dart';

class ResetPasswordScreen extends HookConsumerWidget {
  const ResetPasswordScreen({
    super.key,
    required this.email,
    this.accessToken,
    this.refreshToken,
  });

  final String email;
  final String? accessToken;
  final String? refreshToken;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController(text: email);
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final isPasswordVisible = useState(false);
    final isConfirmPasswordVisible = useState(false);
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);

    // Listen to auth state changes
    ref.listen<AuthState>(authNotifierProvider, (previous, next) {
      if (next.isError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.errorMessage ?? 'An error occurred'.hardcoded),
            backgroundColor: Colors.red,
          ),
        );
      } else if (next.isInitial && (previous?.isLoading ?? false)) {
        // Password updated successfully
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Password updated successfully'.hardcoded),
            backgroundColor: Colors.green,
          ),
        );
        // Navigate to login
        context.go('/auth');
      }
    });

    return OnboardingScaffold(
      topBar: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppBarBackButton(onTap: () => context.pop()),
          const LanguageButton(),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 60),

          // FitGo Logo
          AImage(imgPath: APath.appLogo, width: 120),

          const SizedBox(height: 60),

          // Title
          TextWidget(
            'Reset Password'.hardcoded,
            style: ATextStyle.title,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Description
          TextWidget(
            'You can update your account login details by entering your new password.'
                .hardcoded,
            style: ATextStyle.description,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 40),

          // Form
          Expanded(
            child: SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Email label
                    TextWidget(
                      'E-Mail'.hardcoded,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Email input (read-only)
                    TextFormField(
                      controller: emailController,
                      readOnly: true,
                      style: ATextStyle.medium.copyWith(color: Colors.black),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Password label
                    TextWidget(
                      'Password'.hardcoded,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Password input
                    TextFormField(
                      controller: passwordController,
                      obscureText: !isPasswordVisible.value,
                      style: ATextStyle.medium.copyWith(color: Colors.black),
                      decoration: InputDecoration(
                        hintText: '••••••••',
                        hintStyle: ATextStyle.medium.copyWith(
                          color: Colors.grey[600],
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            isPasswordVisible.value
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: Colors.grey[600],
                          ),
                          onPressed: () {
                            isPasswordVisible.value = !isPasswordVisible.value;
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your password'.hardcoded;
                        }
                        if (value.length < 6) {
                          return 'Password must be at least 6 characters'
                              .hardcoded;
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 24),

                    // Confirm Password label
                    TextWidget(
                      'Password Again'.hardcoded,
                      style: ATextStyle.medium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Confirm Password input
                    TextFormField(
                      controller: confirmPasswordController,
                      obscureText: !isConfirmPasswordVisible.value,
                      style: ATextStyle.medium.copyWith(color: Colors.black),
                      decoration: InputDecoration(
                        hintText: '••••••••',
                        hintStyle: ATextStyle.medium.copyWith(
                          color: Colors.grey[600],
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                        suffixIcon: IconButton(
                          icon: Icon(
                            isConfirmPasswordVisible.value
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: Colors.grey[600],
                          ),
                          onPressed: () {
                            isConfirmPasswordVisible.value =
                                !isConfirmPasswordVisible.value;
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please confirm your password'.hardcoded;
                        }
                        if (value != passwordController.text) {
                          return 'Passwords do not match'.hardcoded;
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 40),

                    // Update button
                    RaisedButtonWidget(
                      width: double.infinity,
                      text: authState.isLoading ? '' : 'Update'.hardcoded,
                      borderRadius: 15,
                      fontColor: AColor.buttonTextColor,
                      bgColor: AColor.buttonColor,
                      fontStyle: ATextStyle.buttonText,
                      borderSide: BorderSide(
                        color: AColor.textSecondaryColor,
                        width: 2,
                      ),
                      onPressed:
                          authState.isLoading
                              ? null
                              : () {
                                if (formKey.currentState!.validate()) {
                                  authNotifier.updatePassword(
                                    passwordController.text.trim(),
                                    accessToken,
                                    refreshToken,
                                  );
                                }
                              },
                      child:
                          authState.isLoading
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : null,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
