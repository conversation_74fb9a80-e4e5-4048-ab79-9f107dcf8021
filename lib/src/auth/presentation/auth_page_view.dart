import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/auth/domain/profile.dart';
import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/providers/auth_provider.dart';
import 'package:fitgo_app/src/shared/providers/supabase_provider.dart';
import 'package:fitgo_app/src/shared/constants/app_fonts.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/enums/regex_type.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';

import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/shared/extensions/string_extensions.dart';
import 'package:fitgo_app/src/shared/utils/dt_util/dt_util.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/app_bar_back_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_form_field/custom_text_form_field.dart';
import 'package:fitgo_app/src/shared/widgets/phone_input_field/phone_input_field.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:country_code_picker/country_code_picker.dart';

import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/language_button.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:styled_text/styled_text.dart';

part 'components/login_bottom_sheet.dart';
part 'components/register_bottom_sheet.dart';

class AuthPageView extends HookConsumerWidget {
  const AuthPageView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to auth state changes
    ref.listen<bool>(isAuthenticatedProvider, (previous, next) {
      if (next && context.mounted) {
        debugPrint('🔄 Auth state changed - user is now authenticated');

        // Navigate based on user type
        final userType = ref.read(currentUserTypeProvider);
        debugPrint('👤 Current user type: $userType');

        if (userType == UserType.instructor) {
          debugPrint('🚀 Navigating instructor to: /instructor-main');
          context.go('/instructor-main');
        } else {
          // For students, check enrollment status
          final isEnrolled = ref.read(studentEnrollmentStatusProvider);
          debugPrint('🎓 Student enrollment status: $isEnrolled');
          if (isEnrolled) {
            debugPrint('🚀 Navigating student to: /dashboard');
            context.go('/dashboard');
          } else {
            debugPrint('🚀 Navigating student to: /course-list');
            context.go('/course-list');
          }
        }
      }
    });

    return OnboardingScaffold(
      topBar: Align(alignment: Alignment.topRight, child: LanguageButton()),
      sheet: const LoginBottomSheet(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 80),
          AImage(imgPath: APath.appLogo, width: context.width / 2),
        ],
      ),
    );
  }
}
