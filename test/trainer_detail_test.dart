import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/student/presentation/trainer_detail_view.dart';

void main() {
  group('TrainerDetailView Tests', () {
    testWidgets('should display loading state initially', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Verify loading indicator is shown
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display trainer detail screen structure', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Verify the basic structure is present
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(SafeArea), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should have back button functionality', (WidgetTester tester) async {
      // Build the widget with a navigator
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => const TrainerDetailView(trainerId: 'trainer_001'),
                      ),
                    );
                  },
                  child: const Text('Go to Trainer Detail'),
                ),
              ),
            ),
          ),
        ),
      );

      // Tap the button to navigate to trainer detail
      await tester.tap(find.text('Go to Trainer Detail'));
      await tester.pumpAndSettle();

      // Find and tap the back button
      final backButton = find.byIcon(Icons.arrow_back_ios_new);
      expect(backButton, findsOneWidget);

      await tester.tap(backButton);
      await tester.pumpAndSettle();

      // Verify we're back to the original page
      expect(find.text('Go to Trainer Detail'), findsOneWidget);
    });
  });
}
