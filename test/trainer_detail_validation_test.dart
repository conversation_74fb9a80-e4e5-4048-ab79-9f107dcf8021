import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/student/presentation/trainer_detail_view.dart';
import 'package:fitgo_app/src/student/presentation/components/work_history_card.dart';
import 'package:fitgo_app/src/student/presentation/components/certification_card.dart';
import 'package:fitgo_app/src/student/presentation/components/review_card.dart';
import 'package:fitgo_app/src/student/domain/trainer_detail.dart';

void main() {
  group('TrainerDetailView Validation Tests', () {
    testWidgets('should display all UI components correctly', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Verify basic structure is present
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(SafeArea), findsOneWidget);
      
      // Should show loading initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should handle invalid trainer ID gracefully', (WidgetTester tester) async {
      // Build the widget with invalid ID
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'invalid_trainer'),
          ),
        ),
      );

      // Should show loading initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      
      // Wait for error state
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Should show error state (if Supabase is available)
      // Note: This test may timeout in test environment without Supabase
    });

    testWidgets('should display back button', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Should have back button
      expect(find.byIcon(Icons.arrow_back_ios_new), findsOneWidget);
    });

    testWidgets('should display book session button', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for potential data load
      await tester.pumpAndSettle();

      // Should have book session button (may be in loading or loaded state)
      expect(find.text('Book a Session'), findsWidgets);
    });

    testWidgets('should handle navigation correctly', (WidgetTester tester) async {
      // Build with navigation context
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => const TrainerDetailView(trainerId: 'trainer_001'),
                      ),
                    );
                  },
                  child: const Text('Go to Detail'),
                ),
              ),
            ),
          ),
        ),
      );

      // Navigate to detail
      await tester.tap(find.text('Go to Detail'));
      await tester.pumpAndSettle();

      // Should be on detail page
      expect(find.byType(TrainerDetailView), findsOneWidget);

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back_ios_new));
      await tester.pumpAndSettle();

      // Should be back to original page
      expect(find.text('Go to Detail'), findsOneWidget);
    });
  });

  group('UI Component Tests', () {
    testWidgets('WorkHistoryCard should display correctly', (WidgetTester tester) async {
      final workHistory = WorkHistory(
        gym: 'Test Gym',
        role: 'Test Role',
        duration: '2 years',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WorkHistoryCard(workHistory: workHistory),
          ),
        ),
      );

      expect(find.text('Test Gym'), findsOneWidget);
      expect(find.text('Test Role'), findsOneWidget);
      expect(find.text('2 years'), findsOneWidget);
    });

    testWidgets('CertificationCard should display correctly', (WidgetTester tester) async {
      final certification = Certification(
        title: 'Test Certification',
        organization: 'Test Organization',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CertificationCard(certification: certification),
          ),
        ),
      );

      expect(find.text('Test Certification'), findsOneWidget);
      expect(find.text('Test Organization'), findsOneWidget);
    });

    testWidgets('ReviewCard should display correctly', (WidgetTester tester) async {
      final review = Review(
        id: '1',
        clientName: 'Test Client',
        avatarUrl: '',
        rating: 4.5,
        comment: 'Great trainer!',
        createdAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ReviewCard(review: review),
          ),
        ),
      );

      expect(find.text('Test Client'), findsOneWidget);
      expect(find.text('Great trainer!'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsWidgets);
    });
  });

  group('Data Model Tests', () {
    test('WorkHistory should serialize correctly', () {
      final workHistory = WorkHistory(
        gym: 'Test Gym',
        role: 'Test Role',
        duration: '2 years',
      );

      final json = workHistory.toJson();
      expect(json['gym'], equals('Test Gym'));
      expect(json['role'], equals('Test Role'));
      expect(json['duration'], equals('2 years'));

      final fromJson = WorkHistory.fromJson(json);
      expect(fromJson.gym, equals('Test Gym'));
      expect(fromJson.role, equals('Test Role'));
      expect(fromJson.duration, equals('2 years'));
    });

    test('Certification should serialize correctly', () {
      final certification = Certification(
        title: 'Test Cert',
        organization: 'Test Org',
      );

      final json = certification.toJson();
      expect(json['title'], equals('Test Cert'));
      expect(json['organization'], equals('Test Org'));

      final fromJson = Certification.fromJson(json);
      expect(fromJson.title, equals('Test Cert'));
      expect(fromJson.organization, equals('Test Org'));
    });

    test('Review should serialize correctly', () {
      final now = DateTime.now();
      final review = Review(
        id: '1',
        clientName: 'Test Client',
        avatarUrl: 'test.jpg',
        rating: 4.5,
        comment: 'Great!',
        createdAt: now,
      );

      final json = review.toJson();
      expect(json['id'], equals('1'));
      expect(json['client_name'], equals('Test Client'));
      expect(json['avatar_url'], equals('test.jpg'));
      expect(json['rating'], equals(4.5));
      expect(json['comment'], equals('Great!'));

      final fromJson = Review.fromJson(json);
      expect(fromJson.id, equals('1'));
      expect(fromJson.clientName, equals('Test Client'));
      expect(fromJson.avatarUrl, equals('test.jpg'));
      expect(fromJson.rating, equals(4.5));
      expect(fromJson.comment, equals('Great!'));
    });
  });
}
