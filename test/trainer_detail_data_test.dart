import 'package:flutter_test/flutter_test.dart';
import 'package:fitgo_app/src/student/application/trainer_detail_provider.dart';

void main() {
  group('Trainer Detail Data Tests', () {
    test('should fetch trainer detail data from repository', () async {
      // This test will help us verify if the data fetching works
      final repository = TrainerDetailRepository();
      
      try {
        // Test with trainer_001 which we know has data
        final trainerDetail = await repository.getTrainerDetail('trainer_001');
        
        print('Trainer Detail fetched successfully:');
        print('Name: ${trainerDetail.name}');
        print('Work History count: ${trainerDetail.workHistory.length}');
        print('Certifications count: ${trainerDetail.certifications.length}');
        print('Reviews count: ${trainerDetail.reviews.length}');
        
        // Basic assertions
        expect(trainerDetail.name, isNotEmpty);
        expect(trainerDetail.id, equals('trainer_001'));
        
        // Print detailed data for debugging
        if (trainerDetail.workHistory.isNotEmpty) {
          print('Work History:');
          for (final work in trainerDetail.workHistory) {
            print('  - ${work.role} at ${work.gym} (${work.duration})');
          }
        } else {
          print('No work history found');
        }
        
        if (trainerDetail.certifications.isNotEmpty) {
          print('Certifications:');
          for (final cert in trainerDetail.certifications) {
            print('  - ${cert.title} from ${cert.organization}');
          }
        } else {
          print('No certifications found');
        }
        
        if (trainerDetail.reviews.isNotEmpty) {
          print('Reviews:');
          for (final review in trainerDetail.reviews) {
            print('  - ${review.clientName}: ${review.rating}/5 - ${review.comment}');
          }
        } else {
          print('No reviews found');
        }
        
      } catch (e) {
        print('Error fetching trainer detail: $e');
        fail('Failed to fetch trainer detail: $e');
      }
    });

    test('should handle invalid trainer ID', () async {
      final repository = TrainerDetailRepository();
      
      try {
        await repository.getTrainerDetail('invalid_trainer_id');
        fail('Should have thrown an exception for invalid trainer ID');
      } catch (e) {
        print('Expected error for invalid trainer ID: $e');
        expect(e.toString(), contains('not found'));
      }
    });

    test('should fetch data for trainer_002', () async {
      final repository = TrainerDetailRepository();
      
      try {
        final trainerDetail = await repository.getTrainerDetail('trainer_002');
        
        print('Trainer 002 Detail:');
        print('Name: ${trainerDetail.name}');
        print('Work History count: ${trainerDetail.workHistory.length}');
        print('Certifications count: ${trainerDetail.certifications.length}');
        print('Reviews count: ${trainerDetail.reviews.length}');
        
        expect(trainerDetail.name, isNotEmpty);
        expect(trainerDetail.id, equals('trainer_002'));
        
      } catch (e) {
        print('Error fetching trainer_002 detail: $e');
        fail('Failed to fetch trainer_002 detail: $e');
      }
    });
  });
}
