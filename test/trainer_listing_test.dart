import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/student/presentation/course_list_view.dart';

void main() {
  group('CourseListView Tests', () {
    testWidgets('should display trainer listing screen with all components', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const CourseListView(),
          ),
        ),
      );

      // Verify the main components are present
      expect(find.text('FitGo'), findsOneWidget);
      expect(find.text('Search trainers...'), findsOneWidget);
      expect(find.text('Featured Trainers'), findsOneWidget);
      expect(find.text('Highest Rated'), findsOneWidget);
      
      // Verify filter tabs
      expect(find.text('All'), findsOneWidget);
      expect(find.text('Strength'), findsOneWidget);
      expect(find.text('Cardio'), findsOneWidget);
      expect(find.text('Yoga'), findsOneWidget);
      expect(find.text('Pilates'), findsOneWidget);
      
      // Verify trainer cards are displayed
      expect(find.text('<PERSON>'), findsOneWidget);
      expect(find.text('Elite Fitness Center'), findsOneWidget);
      expect(find.text('Strength & HIIT'), findsOneWidget);
      expect(find.text('\$85'), findsOneWidget);
      expect(find.text('per session'), findsAtLeastNWidgets(1));
    });

    testWidgets('should display multiple trainer cards', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerListingView(),
          ),
        ),
      );

      // Verify multiple trainers are displayed
      expect(find.text('Sarah Johnson'), findsOneWidget);
      expect(find.text('Mike Chen'), findsOneWidget);
      expect(find.text('Emma Wilson'), findsOneWidget);
      expect(find.text('David Rodriguez'), findsOneWidget);
      expect(find.text('Lisa Thompson'), findsOneWidget);
    });

    testWidgets('should display rating components correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerListingView(),
          ),
        ),
      );

      // Verify rating stars and review counts
      expect(find.byIcon(Icons.star), findsAtLeastNWidgets(5)); // At least 5 trainers
      expect(find.textContaining('4.'), findsAtLeastNWidgets(5)); // Rating values
      expect(find.textContaining('reviews'), findsAtLeastNWidgets(5)); // Review text
    });

    testWidgets('should have proper dark theme colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerListingView(),
          ),
        ),
      );

      // Find the main scaffold
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, const Color(0xFF111827));
    });
  });

  group('TrainerCard Tests', () {
    testWidgets('should display trainer information correctly', (WidgetTester tester) async {
      final trainer = TrainerData(
        name: 'Test Trainer',
        gym: 'Test Gym',
        rating: 4.5,
        reviewCount: 100,
        specialty: 'Test Specialty',
        price: 50,
        imageUrl: 'test_url',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TrainerCard(trainer: trainer),
          ),
        ),
      );

      expect(find.text('Test Trainer'), findsOneWidget);
      expect(find.text('Test Gym'), findsOneWidget);
      expect(find.text('4.5'), findsOneWidget);
      expect(find.text('(100 reviews)'), findsOneWidget);
      expect(find.text('Test Specialty'), findsOneWidget);
      expect(find.text('\$50'), findsOneWidget);
      expect(find.text('per session'), findsOneWidget);
    });
  });

  group('FilterTab Tests', () {
    testWidgets('should display selected and unselected states correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Row(
              children: [
                FilterTab(
                  text: 'Selected',
                  isSelected: true,
                  onTap: () {},
                ),
                FilterTab(
                  text: 'Unselected',
                  isSelected: false,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Selected'), findsOneWidget);
      expect(find.text('Unselected'), findsOneWidget);
    });
  });

  group('RatingRow Tests', () {
    testWidgets('should display rating and review count', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: RatingRow(
              rating: 4.8,
              reviewCount: 150,
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.star), findsOneWidget);
      expect(find.text('4.8'), findsOneWidget);
      expect(find.text('(150 reviews)'), findsOneWidget);
    });
  });
}
