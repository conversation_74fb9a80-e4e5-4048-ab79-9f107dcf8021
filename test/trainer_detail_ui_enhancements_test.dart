import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/student/presentation/trainer_detail_view.dart';

void main() {
  group('TrainerDetailView UI Enhancements Tests', () {
    testWidgets('should display FloatingActionButton with Turkish text', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for potential data load
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should have floating action button
      expect(find.byType(FloatingActionButton), findsOneWidget);
      
      // Should have Turkish text
      expect(find.text('<PERSON><PERSON><PERSON><PERSON><PERSON>'), findsOneWidget);
      
      // Should have calendar icon
      expect(find.byIcon(Icons.calendar_today), findsOneWidget);
    });

    testWidgets('should display Show All/Show Less button when more than 2 reviews', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should show "Show All" button if there are more than 2 reviews
      expect(find.text('Show All'), findsWidgets);
      
      // Should show expand_more icon
      expect(find.byIcon(Icons.expand_more), findsWidgets);
    });

    testWidgets('should expand reviews when Show All is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Find and tap Show All button
      final showAllButton = find.text('Show All');
      if (showAllButton.evaluate().isNotEmpty) {
        await tester.tap(showAllButton.first);
        await tester.pumpAndSettle();

        // Should now show "Show Less" text
        expect(find.text('Show Less'), findsOneWidget);
        
        // Should show expand_less icon
        expect(find.byIcon(Icons.expand_less), findsOneWidget);
      }
    });

    testWidgets('should collapse reviews when Show Less is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Find and tap Show All button first
      final showAllButton = find.text('Show All');
      if (showAllButton.evaluate().isNotEmpty) {
        await tester.tap(showAllButton.first);
        await tester.pumpAndSettle();

        // Now tap Show Less
        final showLessButton = find.text('Show Less');
        await tester.tap(showLessButton);
        await tester.pumpAndSettle();

        // Should be back to "Show All"
        expect(find.text('Show All'), findsOneWidget);
        expect(find.byIcon(Icons.expand_more), findsOneWidget);
      }
    });

    testWidgets('should not show Show All button when 2 or fewer reviews', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_004'), // Has exactly 2 reviews
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should NOT show "Show All" button
      expect(find.text('Show All'), findsNothing);
      expect(find.byIcon(Icons.expand_more), findsNothing);
    });

    testWidgets('should handle FloatingActionButton tap', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Find and tap the floating action button
      final fabButton = find.byType(FloatingActionButton);
      if (fabButton.evaluate().isNotEmpty) {
        await tester.tap(fabButton);
        await tester.pumpAndSettle();

        // Should trigger booking dialog (if implemented)
        // This test verifies the button is tappable
      }
    });

    testWidgets('should display proper bottom padding for floating button', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should have extra bottom padding in scroll view
      expect(find.byType(SizedBox), findsWidgets);
    });

    testWidgets('should use proper styling for Show All/Show Less button', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should have proper container styling
      expect(find.byType(GestureDetector), findsWidgets);
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('should maintain scroll position during review expansion', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Scroll to reviews section
      await tester.scrollUntilVisible(
        find.text('Client Reviews'),
        500.0,
      );

      // Find and tap Show All button
      final showAllButton = find.text('Show All');
      if (showAllButton.evaluate().isNotEmpty) {
        await tester.tap(showAllButton.first);
        await tester.pumpAndSettle();

        // Reviews section should still be visible
        expect(find.text('Client Reviews'), findsOneWidget);
      }
    });
  });

  group('FloatingActionButton Styling Tests', () {
    testWidgets('should have proper dark theme styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: ThemeData.dark(),
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should have floating action button with proper styling
      final fab = tester.widget<FloatingActionButton>(find.byType(FloatingActionButton));
      
      // Verify it's an extended FAB
      expect(fab, isA<FloatingActionButton>());
    });

    testWidgets('should be positioned at bottom center', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const TrainerDetailView(trainerId: 'trainer_001'),
          ),
        ),
      );

      // Wait for data load
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should have proper positioning
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.floatingActionButtonLocation, FloatingActionButtonLocation.centerFloat);
    });
  });
}
