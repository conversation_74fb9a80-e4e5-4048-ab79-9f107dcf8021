import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/student/presentation/course_list_view.dart';
import 'package:fitgo_app/src/student/application/trainer_list_provider.dart';
import 'package:fitgo_app/src/student/domain/trainer_list.dart';

void main() {
  group('Trainer List Integration Tests', () {
    testWidgets('should display trainer list screen with all components', (
      WidgetTester tester,
    ) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: const CourseListView())),
      );

      // Verify basic structure is present
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(SafeArea), findsOneWidget);

      // Verify top section components
      expect(find.text('Fitgo'), findsOneWidget);
      expect(find.byIcon(Icons.menu), findsOneWidget);

      // Verify search bar
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Search trainers...'), findsOneWidget);

      // Verify filter tabs
      expect(find.text('All'), findsOneWidget);
      expect(find.text('Strength Training'), findsOneWidget);
      expect(find.text('Cardio'), findsOneWidget);

      // Verify featured section
      expect(find.text('Featured Trainers'), findsOneWidget);
      expect(find.text('Highest Rated'), findsOneWidget);
    });

    testWidgets('should show loading indicator initially', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: const CourseListView())),
      );

      // Should show loading indicator while data is being fetched
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should handle search input', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: const CourseListView())),
      );

      // Find the search field and enter text
      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);

      await tester.enterText(searchField, 'Michael');
      await tester.pump();

      // Verify text was entered
      expect(find.text('Michael'), findsOneWidget);
    });

    testWidgets('should handle filter tab selection', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: const CourseListView())),
      );

      // Find and tap a filter tab
      final strengthTab = find.text('Strength Training');
      expect(strengthTab, findsOneWidget);

      await tester.tap(strengthTab);
      await tester.pump();

      // The tap should be handled (no errors)
    });

    testWidgets('should display refresh indicator', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(child: MaterialApp(home: const CourseListView())),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // Find the RefreshIndicator
      expect(find.byType(RefreshIndicator), findsOneWidget);
    });

    testWidgets('should navigate back when back button is tapped', (
      WidgetTester tester,
    ) async {
      // Build the widget with a navigator
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder:
                    (context) => ElevatedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => const CourseListView(),
                          ),
                        );
                      },
                      child: const Text('Go to Trainer List'),
                    ),
              ),
            ),
          ),
        ),
      );

      // Tap the button to navigate to trainer list
      await tester.tap(find.text('Go to Trainer List'));
      await tester.pumpAndSettle();

      // Verify we're on the trainer list page
      expect(find.text('Fitgo'), findsOneWidget);

      // Find and tap the back button
      final backButton = find.byIcon(Icons.arrow_back_ios_new);
      expect(backButton, findsOneWidget);

      await tester.tap(backButton);
      await tester.pumpAndSettle();

      // Verify we're back to the original page
      expect(find.text('Go to Trainer List'), findsOneWidget);
    });
  });

  group('TrainerListFilter Tests', () {
    test('should create filter with default values', () {
      const filter = TrainerListFilter();

      expect(filter.searchQuery, isNull);
      expect(filter.specialization, isNull);
      expect(filter.minRating, isNull);
      expect(filter.maxPrice, isNull);
      expect(filter.sortBy, equals('rating'));
      expect(filter.ascending, isFalse);
    });

    test('should create filter with custom values', () {
      const filter = TrainerListFilter(
        searchQuery: 'Michael',
        specialization: 'Strength Training',
        minRating: 4.5,
        maxPrice: 100,
        sortBy: 'name',
        ascending: true,
      );

      expect(filter.searchQuery, equals('Michael'));
      expect(filter.specialization, equals('Strength Training'));
      expect(filter.minRating, equals(4.5));
      expect(filter.maxPrice, equals(100));
      expect(filter.sortBy, equals('name'));
      expect(filter.ascending, isTrue);
    });

    test('should copy filter with new values', () {
      const originalFilter = TrainerListFilter(
        searchQuery: 'Michael',
        specialization: 'Strength Training',
      );

      final newFilter = originalFilter.copyWith(
        searchQuery: 'Sarah',
        minRating: 4.0,
      );

      expect(newFilter.searchQuery, equals('Sarah'));
      expect(newFilter.specialization, equals('Strength Training'));
      expect(newFilter.minRating, equals(4.0));
    });
  });

  group('TrainerListItem Tests', () {
    test('should create TrainerListItem from JSON', () {
      final json = {
        'id': 'trainer_001',
        'name': 'Michael Anderson',
        'gym': 'Elite Fitness',
        'rating': 4.8,
        'review_count': 156,
        'specialty': 'Strength Training',
        'price_per_session': 85,
        'photo_url': 'https://example.com/photo.jpg',
        'specializations': ['Strength Training', 'HIIT'],
        'experience_years': 8,
        'client_count': 200,
      };

      final trainer = TrainerListItem.fromJson(json);

      expect(trainer.id, equals('trainer_001'));
      expect(trainer.name, equals('Michael Anderson'));
      expect(trainer.gym, equals('Elite Fitness'));
      expect(trainer.rating, equals(4.8));
      expect(trainer.reviewCount, equals(156));
      expect(trainer.specialty, equals('Strength Training'));
      expect(trainer.price, equals(85));
      expect(trainer.imageUrl, equals('https://example.com/photo.jpg'));
      expect(trainer.specializations, equals(['Strength Training', 'HIIT']));
      expect(trainer.experienceYears, equals(8));
      expect(trainer.clientCount, equals(200));
    });

    test('should handle missing JSON fields with defaults', () {
      final json = {'id': 'trainer_001', 'name': 'Michael Anderson'};

      final trainer = TrainerListItem.fromJson(json);

      expect(trainer.id, equals('trainer_001'));
      expect(trainer.name, equals('Michael Anderson'));
      expect(trainer.gym, equals('Fitness Center'));
      expect(trainer.rating, equals(0.0));
      expect(trainer.reviewCount, equals(0));
      expect(trainer.specialty, equals('General Fitness'));
      expect(trainer.price, equals(50));
      expect(trainer.imageUrl, equals(''));
      expect(trainer.specializations, equals([]));
      expect(trainer.experienceYears, equals(0));
      expect(trainer.clientCount, equals(0));
    });

    test('should convert TrainerListItem to JSON', () {
      final trainer = TrainerListItem(
        id: 'trainer_001',
        name: 'Michael Anderson',
        gym: 'Elite Fitness',
        rating: 4.8,
        reviewCount: 156,
        specialty: 'Strength Training',
        price: 85,
        imageUrl: 'https://example.com/photo.jpg',
        specializations: ['Strength Training', 'HIIT'],
        experienceYears: 8,
        clientCount: 200,
        maxCapacity: 50,
        currentEnrollment: 25,
        availableSpots: 25,
      );

      final json = trainer.toJson();

      expect(json['id'], equals('trainer_001'));
      expect(json['name'], equals('Michael Anderson'));
      expect(json['gym'], equals('Elite Fitness'));
      expect(json['rating'], equals(4.8));
      expect(json['review_count'], equals(156));
      expect(json['specialty'], equals('Strength Training'));
      expect(json['price_per_session'], equals(85));
      expect(json['photo_url'], equals('https://example.com/photo.jpg'));
      expect(json['specializations'], equals(['Strength Training', 'HIIT']));
      expect(json['experience_years'], equals(8));
      expect(json['client_count'], equals(200));
    });
  });
}
