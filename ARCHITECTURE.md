# FitGo App Architecture

This document describes the clean architecture implementation following <PERSON><PERSON> and <PERSON>'s best practices for Flutter apps with Riverpod.

## Architecture Overview

The app follows a layered architecture with clear separation of concerns:

```
lib/
├── main.dart                          # App entry point
├── src/
│   ├── app.dart                       # Main app widget
│   ├── app_provider.dart              # Global app state providers
│   └── shared/
│       ├── providers/                 # Shared providers
│       │   ├── app_startup_provider.dart
│       │   ├── supabase_provider.dart
│       │   └── auth_provider.dart
│       └── services/                  # Service layer
│           ├── supabase_service.dart
│           └── auth_service.dart
├── core/
│   ├── environment/                   # Environment configuration
│   └── local_storage/                 # Local storage abstraction
└── features/                          # Feature modules
    └── auth/
        ├── application/               # Business logic
        ├── domain/                    # Domain models
        ├── repository/                # Data access layer
        └── presentation/              # UI layer
```

## Key Components

### 1. Environment Configuration

Environment-specific configuration is handled through:
- `IEnvironment` interface
- `_DevelopmentEnvironment` and `_ProductionEnvironment` implementations
- Environment selection via `--dart-define envConfig=development|production`

### 2. Dependency Injection with Riverpod

The app uses Riverpod providers for dependency injection:

```dart
// Service providers
final supabaseServiceProvider = Provider<ISupabaseService>((ref) {
  final environment = ref.watch(environmentProvider);
  return SupabaseService(environment);
});

// Repository providers
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final authController = ref.watch(authControllerProvider);
  return AuthRepository(authController);
});
```

### 3. App Initialization

App startup is handled by `appStartupProvider` which:
- Initializes Supabase
- Sets up local storage (Hive)
- Configures auth state listeners
- Handles initialization errors gracefully

### 4. Authentication Flow

The authentication system follows clean architecture:

```
UI Layer (Presentation)
    ↓
Application Layer (AuthNotifier)
    ↓
Repository Layer (AuthRepository)
    ↓
Service Layer (AuthService)
    ↓
External Service (Supabase)
```

## Usage Examples

### Environment Setup

Run the app with different environments:

```bash
# Development
flutter run --dart-define envConfig=development

# Production
flutter run --dart-define envConfig=production
```

### Using Authentication

```dart
class LoginScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);

    return authState.when(
      initial: () => LoginForm(
        onSignIn: (email, password) => authNotifier.signInWithEmail(
          email: email,
          password: password,
        ),
      ),
      loading: () => LoadingWidget(),
      authenticated: () => HomeScreen(),
      unauthenticated: () => LoginForm(),
      error: (message) => ErrorWidget(message),
    );
  }
}
```

### Accessing Services

```dart
class SomeWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Access current user
    final currentUser = ref.watch(currentUserProvider);
    
    // Access auth controller
    final authController = ref.read(authControllerProvider);
    
    // Access environment
    final environment = ref.read(environmentProvider);
    
    return currentUser.when(
      data: (user) => Text('Hello ${user?.email}'),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('Error: $error'),
    );
  }
}
```

## Best Practices Implemented

1. **Provider Hierarchy**: Clear provider dependencies with proper scoping
2. **Error Handling**: Comprehensive error handling at all layers
3. **State Management**: Immutable state with sealed classes
4. **Separation of Concerns**: Clear boundaries between layers
5. **Testability**: All dependencies are injectable and mockable
6. **Environment Configuration**: Environment-specific settings
7. **Async Initialization**: Proper app startup flow with loading states
8. **User Flow Control**: Prevents premature navigation with plan waiting screens
9. **Consistent UI Theming**: Standardized color usage across navigation components

## Configuration Required

Before running the app, update the environment files with your actual values:

1. `lib/core/environment/development_environment.dart`
2. `lib/core/environment/production_environment.dart`

Replace placeholder values with your actual:
- Supabase URL
- Supabase Anon Key
- API endpoints
- Other service configurations

## Migration from GetIt

The old GetIt-based dependency injection has been completely replaced with Riverpod providers. Key changes:

- Removed `GlobalAppRef` pattern
- Replaced service locator with provider dependency injection
- Improved testability and type safety
- Better integration with Flutter widget lifecycle
