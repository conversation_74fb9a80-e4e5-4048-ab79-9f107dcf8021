
# Changelog

All notable changes to the FitGo app will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Student plan waiting screen with purchase history access
- Bottom navigation icons standardized with AColor.fitgoGreen
- Plan status checking functionality for students

### Changed
- Form approval waiting screen redesigned for better student experience
- Removed navigation back button from plan waiting screen
- Updated bottom navigation colors for both student and instructor views

### Fixed
- Student workflow now prevents premature navigation to main screens

### Added
- Comprehensive README.md with full project documentation
- Enhanced Makefile with FitGo-specific development commands
- Complete project structure documentation
- CHANGELOG.md with detailed version history

### Changed
- Updated app display name from "fitgo_app" to "Fitgo"
- Enhanced development workflow documentation

## [1.0.0] - 2025-01-26

### Added
- **Authentication System**
  - Email/password authentication with Supabase
  - Google, Facebook, and Apple Sign-In integration
  - Secure password reset with domain-based links
  - Wix website integration for password reset handling
  - Deep linking support for mobile app navigation

- **Onboarding & User Management**
  - Persistent onboarding flow with Hive local storage
  - User type selection (Student/Instructor)
  - Multi-step onboarding process
  - Landing page with user type selection
  - Splash screen with intelligent navigation logic

- **Course Management System**
  - Course listing with advanced filtering (rating, price, capacity)
  - Search functionality with real-time filtering
  - Instructor-student enrollment system
  - Course capacity management for instructors
  - Detailed course information views

- **User Interface & Experience**
  - Custom form components with proper validation
  - Country code selection for phone inputs (Turkey +90 default)
  - Date input formatting (DD/MM/YYYY) with automatic slash insertion
  - Responsive design with context extensions
  - Right-side navigation drawer with logout confirmation
  - Dark/Light theme support
  - Multi-language support (English/Turkish)

- **State Management & Architecture**
  - Domain-Driven Design (DDD) with Clean Architecture
  - Riverpod state management with StateNotifier pattern
  - Repository pattern for data access
  - Comprehensive error handling and logging
  - Provider-based dependency injection

- **Database & Backend**
  - Supabase integration with Row Level Security (RLS)
  - Unified instructor table (merged teachers/trainers)
  - User profiles with fitness goals and photos
  - Enrollment tracking and management
  - Secure data access with proper authentication

- **Development Tools**
  - Comprehensive testing setup
  - Code generation with build_runner
  - Localization support with flutter_localizations
  - Static analysis with very_good_analysis
  - Automated formatting and linting

### Fixed
- **Navigation Issues**
  - Fixed Navigator assertion errors during registration
  - Resolved sequential navigation conflicts with proper delays
  - Fixed logout navigation to correct auth route

- **UI/UX Improvements**
  - Fixed keyboard overflow issues in form screens
  - Resolved phone input field border radius issues
  - Fixed password visibility toggle functionality
  - Improved search and filter state management

- **Authentication & Storage**
  - Fixed Supabase connection errors in splash screen
  - Resolved StateNotifier usage errors across multiple files
  - Fixed onboarding data persistence and clearing on logout
  - Corrected AuthNotifier constructor parameter mismatch

- **Database & Data Management**
  - Created missing user_profiles table
  - Fixed freezed model syntax issues
  - Resolved repository pattern implementation
  - Fixed enrollment data access and error handling

### Changed
- **Architecture Improvements**
  - Migrated from GetIt to Riverpod dependency injection
  - Enhanced provider hierarchy with proper scoping
  - Improved error handling at all architectural layers
  - Standardized on "instructor" terminology throughout app

- **UI Component Enhancements**
  - Converted TextFormField to CustomTextFormField usage
  - Implemented SingleChildScrollView-Column structure for forms
  - Enhanced onboarding screens with consistent UI patterns
  - Improved drawer positioning and functionality

- **Development Workflow**
  - Updated to use Puro for Flutter version management
  - Enhanced Makefile with comprehensive development commands
  - Improved code organization following DDD principles
  - Standardized error handling and logging patterns

### Security
- **Authentication Security**
  - Implemented secure password reset with domain validation
  - Added Row Level Security (RLS) policies for all database tables
  - Secured API endpoints with proper authentication checks
  - Protected sensitive user data with encryption

- **Data Protection**
  - Implemented proper user data isolation
  - Added secure file upload for profile photos
  - Protected against unauthorized data access
  - Ensured GDPR compliance for user data handling

### Technical Debt
- **Code Quality**
  - Removed unused/empty files and duplicate components
  - Standardized naming conventions across the codebase
  - Improved type safety with proper model definitions
  - Enhanced test coverage for critical functionality

- **Performance**
  - Optimized state management with efficient providers
  - Improved image caching and loading performance
  - Enhanced navigation performance with proper routing
  - Reduced app startup time with optimized initialization

## [0.1.0] - Initial Development

### Added
- Basic Flutter project structure
- Initial Supabase integration
- Basic authentication flow
- Simple navigation setup
- Core UI components

---

## Release Notes

### Version 1.0.0 Highlights

This major release represents a complete transformation of the FitGo app into a professional, production-ready fitness platform. Key achievements include:

1. **Complete Architecture Overhaul**: Migrated to DDD with Clean Architecture principles
2. **Enhanced User Experience**: Comprehensive onboarding and intuitive navigation
3. **Robust Authentication**: Multi-platform sign-in with secure password management
4. **Advanced Course Management**: Full-featured course system with filtering and search
5. **Production-Ready Infrastructure**: Comprehensive testing, documentation, and deployment setup

### Breaking Changes

- **State Management**: Complete migration from GetIt to Riverpod (requires code updates)
- **Database Schema**: Unified instructor table (teachers/trainers merged)
- **Navigation**: Updated routing structure with GoRouter
- **Authentication**: Enhanced security requirements for password reset

### Migration Guide

For developers updating from earlier versions:

1. Update state management calls to use Riverpod providers
2. Update database queries to use unified instructor table
3. Update navigation calls to use new GoRouter structure
4. Update authentication flows to handle new security requirements

### Known Issues

- Firebase integration temporarily disabled (will be re-enabled in future release)
- Some UI text still references "trainer" instead of "instructor" (will be fixed in patch)
- iOS build requires manual configuration for Apple Sign-In

### Upcoming Features (v1.1.0)

- AI-powered workout recommendations
- Advanced progress analytics with charts
- Social features and leaderboards
- In-app purchase integration
- Push notification system
- Video streaming with caching

<!-- ### General Guidelines:
- **(Note)**: note...
- **SQL**: Use ```sql for SQL-related code.
- **Code (other languages)**: Use language-specific identifiers like ```dart, ```typescript, ```bash, etc.
- **Logs/Errors/General Text**: Use ```text or just regular markdown text formatting for descriptions and messages. -->
