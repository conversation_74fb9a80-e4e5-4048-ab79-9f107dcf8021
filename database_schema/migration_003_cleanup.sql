-- Migration 003: Cleanup and Finalization
-- This script removes redundant tables and finalizes the schema refactor
-- ⚠️ WARNING: This script will permanently delete data. Ensure migration is successful before running.

-- =====================================================
-- STEP 1: Verify Migration Success
-- =====================================================

-- Check that all students have been migrated
DO $$
DECLARE
    student_count_old INTEGER;
    student_count_new INTEGER;
BEGIN
    SELECT COUNT(*) INTO student_count_old FROM students;
    SELECT COUNT(*) INTO student_count_new FROM profiles WHERE role = 'student';
    
    IF student_count_new < student_count_old THEN
        RAISE EXCEPTION 'Student migration incomplete: % old records, % new records', 
            student_count_old, student_count_new;
    END IF;
    
    RAISE NOTICE 'Student migration verified: % records migrated', student_count_new;
END $$;

-- Check that all teachers have been migrated
DO $$
DECLARE
    teacher_count_old INTEGER;
    instructor_count_new INTEGER;
BEGIN
    SELECT COUNT(*) INTO teacher_count_old FROM teachers;
    SELECT COUNT(*) INTO instructor_count_new FROM profiles WHERE role = 'instructor';
    
    IF instructor_count_new < teacher_count_old THEN
        RAISE NOTICE 'Teacher migration may be incomplete: % old records, % new instructor records', 
            teacher_count_old, instructor_count_new;
    END IF;
    
    RAISE NOTICE 'Teacher migration status: % old records, % new instructor records', 
        teacher_count_old, instructor_count_new;
END $$;

-- =====================================================
-- STEP 2: Update Foreign Key References
-- =====================================================

-- Update any remaining foreign key references to use the new schema
-- This is a safety step to catch any missed references

-- Update meal_plans if they reference old instructor IDs
-- (This assumes meal_plans.instructor_id should reference profiles.id)

-- Update workout_plans if they reference old instructor IDs
-- (This assumes workout_plans.instructor_id should reference profiles.id)

-- =====================================================
-- STEP 3: Replace Old Tables with New Ones
-- =====================================================

-- Rename old tables to backup versions
ALTER TABLE IF EXISTS payments RENAME TO payments_backup;
ALTER TABLE IF EXISTS trainer_reviews RENAME TO trainer_reviews_backup;

-- Rename new tables to production names
ALTER TABLE payments_new RENAME TO payments;
ALTER TABLE instructor_reviews_new RENAME TO instructor_reviews;

-- =====================================================
-- STEP 4: Update Indexes and Constraints
-- =====================================================

-- Add indexes to new tables for performance
CREATE INDEX IF NOT EXISTS idx_student_profiles_user_id ON student_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_student_profiles_instructor_id ON student_profiles(current_instructor_id);
CREATE INDEX IF NOT EXISTS idx_instructor_profiles_user_id ON instructor_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_instructor_profiles_rating ON instructor_profiles(average_rating);
CREATE INDEX IF NOT EXISTS idx_instructor_profiles_verification ON instructor_profiles(verification_status);

CREATE INDEX IF NOT EXISTS idx_assignments_student_id ON student_instructor_assignments(student_id);
CREATE INDEX IF NOT EXISTS idx_assignments_instructor_id ON student_instructor_assignments(instructor_id);
CREATE INDEX IF NOT EXISTS idx_assignments_active ON student_instructor_assignments(is_active);

CREATE INDEX IF NOT EXISTS idx_instructor_reviews_instructor_id ON instructor_reviews(instructor_id);
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_student_id ON instructor_reviews(student_id);
CREATE INDEX IF NOT EXISTS idx_instructor_reviews_rating ON instructor_reviews(rating);

CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(student_id);
CREATE INDEX IF NOT EXISTS idx_payments_instructor_id ON payments(instructor_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- =====================================================
-- STEP 5: Create Triggers for Data Consistency
-- =====================================================

-- Trigger to update instructor average rating when reviews change
CREATE OR REPLACE FUNCTION update_instructor_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE instructor_profiles 
    SET 
        average_rating = (
            SELECT COALESCE(AVG(rating), 0) 
            FROM instructor_reviews 
            WHERE instructor_id = COALESCE(NEW.instructor_id, OLD.instructor_id)
        ),
        total_reviews = (
            SELECT COUNT(*) 
            FROM instructor_reviews 
            WHERE instructor_id = COALESCE(NEW.instructor_id, OLD.instructor_id)
        ),
        updated_at = NOW()
    WHERE user_id = COALESCE(NEW.instructor_id, OLD.instructor_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for review changes
DROP TRIGGER IF EXISTS trigger_update_instructor_rating ON instructor_reviews;
CREATE TRIGGER trigger_update_instructor_rating
    AFTER INSERT OR UPDATE OR DELETE ON instructor_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_instructor_rating();

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers to all relevant tables
CREATE TRIGGER trigger_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_student_profiles_updated_at
    BEFORE UPDATE ON student_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_instructor_profiles_updated_at
    BEFORE UPDATE ON instructor_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_assignments_updated_at
    BEFORE UPDATE ON student_instructor_assignments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STEP 6: Create Useful Views and Functions
-- =====================================================

-- View for complete student information
CREATE OR REPLACE VIEW student_complete_view AS
SELECT 
    p.id,
    p.email,
    p.first_name,
    p.last_name,
    p.phone,
    p.avatar_url,
    p.date_of_birth,
    p.gender,
    p.bio,
    p.address,
    p.city,
    p.district,
    p.postal_code,
    p.is_active,
    p.created_at,
    p.updated_at,
    sp.program_type,
    sp.subscription_expires_at,
    sp.remaining_sessions,
    sp.fitness_level,
    sp.fitness_goals,
    sp.medical_conditions,
    sp.dietary_restrictions,
    sp.current_weight,
    sp.target_weight,
    sp.height,
    instructor.first_name as instructor_first_name,
    instructor.last_name as instructor_last_name
FROM profiles p
JOIN student_profiles sp ON sp.user_id = p.id
LEFT JOIN profiles instructor ON instructor.id = sp.current_instructor_id
WHERE p.role = 'student';

-- View for complete instructor information
CREATE OR REPLACE VIEW instructor_complete_view AS
SELECT 
    p.id,
    p.email,
    p.first_name,
    p.last_name,
    p.phone,
    p.avatar_url,
    p.date_of_birth,
    p.gender,
    p.bio,
    p.address,
    p.city,
    p.district,
    p.postal_code,
    p.is_active,
    p.created_at,
    p.updated_at,
    ip.specializations,
    ip.certifications,
    ip.experience_years,
    ip.education,
    ip.gym_affiliation,
    ip.department,
    ip.pricing_model,
    ip.session_price,
    ip.total_clients,
    ip.average_rating,
    ip.total_reviews,
    ip.linkedin_url,
    ip.instagram_url,
    ip.website_url,
    ip.is_accepting_clients,
    ip.verification_status
FROM profiles p
JOIN instructor_profiles ip ON ip.user_id = p.id
WHERE p.role = 'instructor';

-- Function to get user's complete profile
CREATE OR REPLACE FUNCTION get_user_complete_profile(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    user_role TEXT;
    result JSON;
BEGIN
    SELECT role INTO user_role FROM profiles WHERE id = user_uuid;
    
    IF user_role = 'student' THEN
        SELECT row_to_json(scv) INTO result 
        FROM student_complete_view scv 
        WHERE scv.id = user_uuid;
    ELSIF user_role = 'instructor' THEN
        SELECT row_to_json(icv) INTO result 
        FROM instructor_complete_view icv 
        WHERE icv.id = user_uuid;
    ELSE
        SELECT row_to_json(p) INTO result 
        FROM profiles p 
        WHERE p.id = user_uuid;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 7: Grant Appropriate Permissions
-- =====================================================

-- Grant permissions for authenticated users
GRANT SELECT, INSERT, UPDATE ON profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE ON student_profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE ON instructor_profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE ON student_instructor_assignments TO authenticated;
GRANT SELECT, INSERT, UPDATE ON instructor_reviews TO authenticated;
GRANT SELECT, INSERT, UPDATE ON payments TO authenticated;

-- Grant permissions on views
GRANT SELECT ON student_complete_view TO authenticated;
GRANT SELECT ON instructor_complete_view TO authenticated;
GRANT SELECT ON students_view TO authenticated;
GRANT SELECT ON teachers_view TO authenticated;

-- =====================================================
-- STEP 8: Final Verification
-- =====================================================

-- Verify that all critical data has been migrated
DO $$
DECLARE
    profile_count INTEGER;
    student_profile_count INTEGER;
    instructor_profile_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO profile_count FROM profiles;
    SELECT COUNT(*) INTO student_profile_count FROM student_profiles;
    SELECT COUNT(*) INTO instructor_profile_count FROM instructor_profiles;
    
    RAISE NOTICE 'Migration completed successfully:';
    RAISE NOTICE '  Total profiles: %', profile_count;
    RAISE NOTICE '  Student profiles: %', student_profile_count;
    RAISE NOTICE '  Instructor profiles: %', instructor_profile_count;
    
    IF profile_count = 0 THEN
        RAISE EXCEPTION 'Migration failed: No profiles found';
    END IF;
END $$;

-- =====================================================
-- STEP 9: Optional - Remove Old Tables (DANGEROUS!)
-- =====================================================

-- ⚠️ UNCOMMENT ONLY AFTER THOROUGH TESTING ⚠️
-- These commands will permanently delete the old tables

-- DROP TABLE IF EXISTS students CASCADE;
-- DROP TABLE IF EXISTS teachers CASCADE;
-- DROP TABLE IF EXISTS trainers CASCADE;
-- DROP TABLE IF EXISTS payments_backup CASCADE;
-- DROP TABLE IF EXISTS trainer_reviews_backup CASCADE;

-- =====================================================
-- STEP 10: Create Migration Log
-- =====================================================

CREATE TABLE IF NOT EXISTS migration_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    migration_name TEXT NOT NULL,
    executed_at TIMESTAMPTZ DEFAULT NOW(),
    status TEXT DEFAULT 'completed',
    notes TEXT
);

INSERT INTO migration_log (migration_name, notes) VALUES 
('001_unified_profiles', 'Created unified profiles table and role extensions'),
('002_data_migration', 'Migrated data from legacy tables to new schema'),
('003_cleanup', 'Cleaned up redundant tables and finalized schema');

RAISE NOTICE 'Schema refactor completed successfully! 🎉';
RAISE NOTICE 'Please test thoroughly before removing backup tables.';
