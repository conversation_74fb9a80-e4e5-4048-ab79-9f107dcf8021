-- Migration 002: Data Migration from Legacy Tables
-- This script migrates data from students, teachers, and trainers tables to the unified schema

-- =====================================================
-- STEP 1: Migrate Students Data to Profiles
-- =====================================================

-- Insert students into profiles table (if not already exists)
INSERT INTO profiles (
    id,
    email,
    first_name,
    last_name,
    phone,
    date_of_birth,
    gender,
    avatar_url,
    role,
    address,
    city,
    district,
    postal_code,
    is_active,
    created_at,
    updated_at
)
SELECT 
    gen_random_uuid() as id,
    s.email,
    s.name as first_name,
    s.surname as last_name,
    s.phone,
    s.birth_date as date_of_birth,
    s.gender::text,
    s.profile_picture_url as avatar_url,
    'student' as role,
    s.address,
    s.city,
    s.district,
    s.postal_code,
    s.is_active,
    s.created_at,
    s.updated_at
FROM students s
WHERE NOT EXISTS (
    SELECT 1 FROM profiles p WHERE p.email = s.email
);

-- Create mapping table for student IDs
CREATE TEMP TABLE student_id_mapping AS
SELECT 
    s.student_id as old_student_id,
    p.id as new_profile_id,
    s.email
FROM students s
JOIN profiles p ON p.email = s.email AND p.role = 'student';

-- =====================================================
-- STEP 2: Migrate Teachers Data to Profiles
-- =====================================================

-- Insert teachers into profiles table (if not already exists)
INSERT INTO profiles (
    id,
    email,
    first_name,
    last_name,
    phone,
    date_of_birth,
    gender,
    avatar_url,
    role,
    address,
    city,
    district,
    postal_code,
    is_active,
    created_at,
    updated_at
)
SELECT 
    gen_random_uuid() as id,
    t.email,
    t.name as first_name,
    t.surname as last_name,
    t.phone,
    t.birth_date as date_of_birth,
    t.gender::text,
    t.profile_picture_url as avatar_url,
    'instructor' as role,
    t.address,
    t.city,
    t.district,
    t.postal_code,
    t.is_active,
    t.created_at,
    t.updated_at
FROM teachers t
WHERE NOT EXISTS (
    SELECT 1 FROM profiles p WHERE p.email = t.email
);

-- Create mapping table for teacher IDs
CREATE TEMP TABLE teacher_id_mapping AS
SELECT 
    t.teacher_id as old_teacher_id,
    p.id as new_profile_id,
    t.email
FROM teachers t
JOIN profiles p ON p.email = t.email AND p.role = 'instructor';

-- =====================================================
-- STEP 3: Migrate Trainers Data to Profiles
-- =====================================================

-- Insert trainers into profiles table (if not already exists)
-- Note: trainers table has limited data, so we'll use defaults for missing fields
INSERT INTO profiles (
    id,
    email,
    first_name,
    last_name,
    avatar_url,
    role,
    is_active,
    created_at,
    updated_at
)
SELECT 
    CASE 
        WHEN tr.id ~ '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$' 
        THEN tr.id::uuid
        ELSE gen_random_uuid()
    END as id,
    COALESCE(tr.id || '@fitgo.com', 'trainer_' || tr.id || '@fitgo.com') as email,
    SPLIT_PART(tr.name, ' ', 1) as first_name,
    CASE 
        WHEN POSITION(' ' IN tr.name) > 0 THEN SUBSTRING(tr.name FROM POSITION(' ' IN tr.name) + 1)
        ELSE ''
    END as last_name,
    tr.photo_url as avatar_url,
    'instructor' as role,
    true as is_active,
    tr.created_at,
    tr.updated_at
FROM trainers tr
WHERE NOT EXISTS (
    SELECT 1 FROM profiles p WHERE p.email = COALESCE(tr.id || '@fitgo.com', 'trainer_' || tr.id || '@fitgo.com')
);

-- Create mapping table for trainer IDs
CREATE TEMP TABLE trainer_id_mapping AS
SELECT 
    tr.id as old_trainer_id,
    p.id as new_profile_id,
    tr.name
FROM trainers tr
JOIN profiles p ON (
    p.email = COALESCE(tr.id || '@fitgo.com', 'trainer_' || tr.id || '@fitgo.com')
    AND p.role = 'instructor'
);

-- =====================================================
-- STEP 4: Create Student Profile Extensions
-- =====================================================

INSERT INTO student_profiles (
    user_id,
    current_instructor_id,
    program_type,
    remaining_sessions,
    created_at,
    updated_at
)
SELECT 
    sim.new_profile_id as user_id,
    tim.new_profile_id as current_instructor_id,
    CASE s.program_type::text
        WHEN 'BASIC' THEN 'basic'
        WHEN 'PREMIUM' THEN 'premium'
        WHEN 'VIP' THEN 'vip'
        ELSE 'basic'
    END as program_type,
    s.remaining_days as remaining_sessions,
    s.created_at,
    s.updated_at
FROM students s
JOIN student_id_mapping sim ON sim.old_student_id = s.student_id
LEFT JOIN teacher_id_mapping tim ON tim.old_teacher_id = s.teacher_id;

-- =====================================================
-- STEP 5: Create Instructor Profile Extensions
-- =====================================================

-- For teachers
INSERT INTO instructor_profiles (
    user_id,
    department,
    pricing_model,
    total_clients,
    average_rating,
    linkedin_url,
    instagram_url,
    website_url,
    created_at,
    updated_at
)
SELECT 
    tim.new_profile_id as user_id,
    t.department,
    jsonb_build_object(
        'monthly', t.price_monthly,
        '3_months', t.price_3_months,
        '6_months', t.price_6_months,
        '1_year', t.price_1_year
    ) as pricing_model,
    t.total_students as total_clients,
    t.average_rating,
    t.linkedin_url,
    t.instagram_url,
    t.website_url,
    t.created_at,
    t.updated_at
FROM teachers t
JOIN teacher_id_mapping tim ON tim.old_teacher_id = t.teacher_id;

-- For trainers
INSERT INTO instructor_profiles (
    user_id,
    specializations,
    experience_years,
    gym_affiliation,
    session_price,
    total_clients,
    average_rating,
    total_reviews,
    created_at,
    updated_at
)
SELECT 
    trim.new_profile_id as user_id,
    tr.specializations,
    tr.experience_years,
    tr.gym as gym_affiliation,
    tr.price_per_session as session_price,
    tr.client_count as total_clients,
    tr.rating as average_rating,
    tr.review_count as total_reviews,
    tr.created_at,
    tr.updated_at
FROM trainers tr
JOIN trainer_id_mapping trim ON trim.old_trainer_id = tr.id
WHERE NOT EXISTS (
    SELECT 1 FROM instructor_profiles ip WHERE ip.user_id = trim.new_profile_id
);

-- =====================================================
-- STEP 6: Migrate Student-Instructor Assignments
-- =====================================================

INSERT INTO student_instructor_assignments (
    student_id,
    instructor_id,
    program_type,
    start_date,
    is_active,
    created_at
)
SELECT 
    sim.new_profile_id as student_id,
    tim.new_profile_id as instructor_id,
    CASE s.program_type::text
        WHEN 'BASIC' THEN 'basic'
        WHEN 'PREMIUM' THEN 'premium'
        WHEN 'VIP' THEN 'vip'
        ELSE 'basic'
    END as program_type,
    s.created_at as start_date,
    s.is_active,
    s.created_at
FROM students s
JOIN student_id_mapping sim ON sim.old_student_id = s.student_id
JOIN teacher_id_mapping tim ON tim.old_teacher_id = s.teacher_id
WHERE s.teacher_id IS NOT NULL;

-- =====================================================
-- STEP 7: Migrate Reviews
-- =====================================================

INSERT INTO instructor_reviews_new (
    instructor_id,
    student_id,
    rating,
    comment,
    created_at
)
SELECT 
    trim.new_profile_id as instructor_id,
    gen_random_uuid() as student_id, -- We'll need to create dummy student profiles for existing reviews
    tr.rating::integer,
    tr.comment,
    tr.created_at
FROM trainer_reviews tr
JOIN trainer_id_mapping trim ON trim.old_trainer_id = tr.trainer_id;

-- =====================================================
-- STEP 8: Migrate Payments
-- =====================================================

INSERT INTO payments_new (
    student_id,
    instructor_id,
    amount,
    program_type,
    duration_months,
    status,
    created_at
)
SELECT 
    sim.new_profile_id as student_id,
    tim.new_profile_id as instructor_id,
    p.amount,
    CASE s.program_type::text
        WHEN 'BASIC' THEN 'basic'
        WHEN 'PREMIUM' THEN 'premium'
        WHEN 'VIP' THEN 'vip'
        ELSE 'basic'
    END as program_type,
    1 as duration_months, -- Default to 1 month, adjust as needed
    'completed' as status, -- Assume existing payments are completed
    p.created_at
FROM payments p
JOIN student_id_mapping sim ON sim.old_student_id = p.student_id
JOIN teacher_id_mapping tim ON tim.old_teacher_id = p.teacher_id;

-- =====================================================
-- STEP 9: Update Foreign Key References
-- =====================================================

-- Update user_enrollments to use new profile IDs
UPDATE user_enrollments ue
SET user_id = sim.new_profile_id
FROM student_id_mapping sim
WHERE ue.user_id::text = sim.old_student_id::text;

-- =====================================================
-- STEP 10: Create Views for Backward Compatibility
-- =====================================================

-- Create a view that mimics the old students table structure
CREATE OR REPLACE VIEW students_view AS
SELECT 
    sp.id::integer as student_id,
    p.first_name as name,
    p.last_name as surname,
    p.email,
    p.phone,
    p.date_of_birth as birth_date,
    p.gender,
    p.avatar_url as profile_picture_url,
    p.address,
    p.city,
    p.district,
    p.postal_code,
    sp.current_instructor_id::integer as teacher_id,
    sp.remaining_sessions as remaining_days,
    sp.program_type,
    p.is_active,
    p.created_at,
    p.updated_at
FROM profiles p
JOIN student_profiles sp ON sp.user_id = p.id
WHERE p.role = 'student';

-- Create a view that mimics the old teachers table structure
CREATE OR REPLACE VIEW teachers_view AS
SELECT 
    ip.id::integer as teacher_id,
    p.first_name as name,
    p.last_name as surname,
    p.email,
    p.phone,
    ip.department,
    p.address,
    p.city,
    p.district,
    p.postal_code,
    (ip.pricing_model->>'monthly')::numeric as price_monthly,
    (ip.pricing_model->>'3_months')::numeric as price_3_months,
    (ip.pricing_model->>'6_months')::numeric as price_6_months,
    (ip.pricing_model->>'1_year')::numeric as price_1_year,
    p.avatar_url as profile_picture_url,
    p.date_of_birth as birth_date,
    p.gender,
    p.is_active,
    ip.linkedin_url,
    ip.instagram_url,
    ip.website_url,
    ip.total_clients as total_students,
    ip.average_rating,
    p.created_at,
    p.updated_at
FROM profiles p
JOIN instructor_profiles ip ON ip.user_id = p.id
WHERE p.role = 'instructor';

-- Drop temporary mapping tables
DROP TABLE student_id_mapping;
DROP TABLE teacher_id_mapping;
DROP TABLE trainer_id_mapping;
