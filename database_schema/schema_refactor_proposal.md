# 🔄 **Supabase Database Schema Refactor Proposal**

## 📊 **Current Issues Analysis**

### ❌ **Major Redundancy Problems:**

1. **Duplicate User Data Across 4 Tables:**
   - `profiles` (base user table with role field)
   - `students` (duplicates: name, surname, email, phone, birth_date, gender, profile_picture_url)
   - `teachers` (duplicates: name, surname, email, phone, birth_date, gender, profile_picture_url)
   - `trainers` (only has name, photo_url - different structure entirely)

2. **Inconsistent Foreign Key References:**
   - Most modern tables reference `profiles.id` (UUID)
   - Legacy tables reference `students.student_id` and `teachers.teacher_id` (integers)
   - `trainers` table uses text IDs and has no connection to `profiles`

3. **Role Logic Confusion:**
   - `profiles.role` field exists but is underutilized
   - `teachers` and `trainers` seem to represent the same concept
   - No clear distinction between instructor types

4. **Data Type Inconsistencies:**
   - Mixed timestamp types (with/without timezone)
   - Different ID types (UUID vs integer vs text)
   - Inconsistent naming conventions

---

## ✅ **Proposed Unified Schema**

### 🎯 **Core Principle: Single Source of Truth**
- **One base user table** (`profiles`) with role-based extensions
- **Consistent UUID primary keys** across all tables
- **Role-specific data** in separate extension tables only when necessary

---

### 📋 **1. Unified Profiles Table (Enhanced)**

```sql
-- Enhanced profiles table as single source of truth
CREATE TABLE profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Auth integration
    auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Basic info (required for all users)
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    phone TEXT,
    avatar_url TEXT,
    date_of_birth DATE,
    gender TEXT CHECK (gender IN ('male', 'female', 'other')),
    bio TEXT,
    
    -- Role and status
    role TEXT NOT NULL DEFAULT 'student' CHECK (role IN ('student', 'instructor', 'admin')),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    
    -- Address (common to all users)
    address TEXT,
    city TEXT,
    district TEXT,
    postal_code TEXT,
    
    -- Metadata
    firebase_token TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Indexes for performance
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_auth_user_id ON profiles(auth_user_id);
CREATE INDEX idx_profiles_is_active ON profiles(is_active);
```

---

### 📋 **2. Role-Specific Extension Tables**

#### **Student Extensions (Only Student-Specific Data)**
```sql
CREATE TABLE student_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Student-specific fields
    current_instructor_id UUID REFERENCES profiles(id),
    program_type TEXT NOT NULL CHECK (program_type IN ('basic', 'premium', 'vip')),
    subscription_expires_at TIMESTAMPTZ,
    remaining_sessions INTEGER DEFAULT 0,
    
    -- Fitness profile
    fitness_level TEXT CHECK (fitness_level IN ('beginner', 'intermediate', 'advanced')),
    fitness_goals TEXT[],
    medical_conditions TEXT,
    dietary_restrictions TEXT[],
    
    -- Progress tracking
    start_weight DECIMAL(5,2),
    current_weight DECIMAL(5,2),
    target_weight DECIMAL(5,2),
    height DECIMAL(5,2),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id),
    CONSTRAINT valid_program_type CHECK (program_type IS NOT NULL)
);
```

#### **Instructor Extensions (Unified Teachers + Trainers)**
```sql
CREATE TABLE instructor_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Professional info
    specializations TEXT[] DEFAULT '{}',
    certifications TEXT[],
    experience_years INTEGER DEFAULT 0,
    education TEXT,
    
    -- Business info
    gym_affiliation TEXT,
    department TEXT,
    license_number TEXT,
    
    -- Pricing (flexible structure)
    pricing_model JSONB DEFAULT '{}', -- {"monthly": 500, "3_months": 1400, "6_months": 2700, "1_year": 5000}
    session_price DECIMAL(10,2),
    
    -- Performance metrics
    total_clients INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.0,
    total_reviews INTEGER DEFAULT 0,
    
    -- Social links
    linkedin_url TEXT,
    instagram_url TEXT,
    website_url TEXT,
    
    -- Status
    is_accepting_clients BOOLEAN DEFAULT true,
    verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id),
    CONSTRAINT valid_experience CHECK (experience_years >= 0),
    CONSTRAINT valid_rating CHECK (average_rating >= 0 AND average_rating <= 5)
);
```

---

### 📋 **3. Relationship Tables (Updated)**

#### **Student-Instructor Assignments**
```sql
CREATE TABLE student_instructor_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES profiles(id),
    instructor_id UUID NOT NULL REFERENCES profiles(id),
    
    -- Assignment details
    program_type TEXT NOT NULL,
    start_date TIMESTAMPTZ DEFAULT NOW(),
    end_date TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    
    -- Progress tracking
    sessions_completed INTEGER DEFAULT 0,
    sessions_total INTEGER,
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(student_id, instructor_id, start_date),
    CONSTRAINT valid_dates CHECK (end_date IS NULL OR end_date > start_date)
);
```

#### **Updated Reviews Table**
```sql
CREATE TABLE instructor_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES profiles(id),
    student_id UUID NOT NULL REFERENCES profiles(id),
    
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(instructor_id, student_id) -- One review per student-instructor pair
);
```

---

### 📋 **4. Updated Payment System**

```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User references (unified)
    student_id UUID NOT NULL REFERENCES profiles(id),
    instructor_id UUID NOT NULL REFERENCES profiles(id),
    
    -- Payment details
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'TRY',
    payment_method TEXT,
    transaction_id TEXT,
    
    -- Program details
    program_type TEXT NOT NULL,
    duration_months INTEGER NOT NULL,
    sessions_included INTEGER,
    
    -- Status
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 🔄 **Migration Strategy**

### **Phase 1: Data Consolidation**
1. **Migrate all user data to unified `profiles` table**
2. **Create role-specific extension tables**
3. **Migrate role-specific data to extensions**

### **Phase 2: Foreign Key Updates**
1. **Update all foreign keys to reference `profiles.id`**
2. **Create mapping tables for legacy ID references**
3. **Update application code to use new schema**

### **Phase 3: Cleanup**
1. **Drop redundant tables (`students`, `teachers`)**
2. **Rename `trainers` → `instructor_profiles`**
3. **Remove duplicate columns**

---

## 📈 **Benefits of This Refactor**

### ✅ **Eliminated Redundancy**
- **Single source of truth** for user data
- **No duplicate name/email/phone fields**
- **Consistent data types and naming**

### ✅ **Improved Performance**
- **Fewer JOINs** required for user queries
- **Better indexing** with consistent UUID keys
- **Reduced storage** from eliminated duplicates

### ✅ **Enhanced Maintainability**
- **Clear role-based architecture**
- **Easier to add new user types**
- **Consistent foreign key relationships**

### ✅ **Better RLS Support**
- **Unified user identification** for security policies
- **Simpler policy definitions**
- **Consistent access control**

---

## 🎯 **Riverpod Integration Benefits**

### **Simplified State Management**
- **Single user provider** instead of multiple role providers
- **Consistent user model** across the application
- **Easier role-based UI rendering**

### **Better Caching**
- **Unified user cache** with role extensions
- **Reduced API calls** with consolidated data
- **Simpler cache invalidation**

---

This refactor eliminates all major redundancies while maintaining flexibility for role-specific features and ensuring scalable development with clean architecture patterns.

---

## 🚀 **Implementation Priority**

### **Immediate Actions (High Priority)**
1. **Create unified `profiles` table structure**
2. **Migrate existing data from `students` and `teachers`**
3. **Update foreign key references in critical tables**

### **Medium Priority**
1. **Create role-specific extension tables**
2. **Update application code to use new schema**
3. **Implement new RLS policies**

### **Low Priority (Cleanup)**
1. **Drop redundant tables after migration**
2. **Optimize indexes and performance**
3. **Update documentation and API contracts**

---

## ⚠️ **Migration Considerations**

### **Data Safety**
- **Backup all data** before migration
- **Test migration** on staging environment
- **Implement rollback procedures**

### **Application Compatibility**
- **Update Riverpod providers** to use new schema
- **Modify API endpoints** for new table structure
- **Update Flutter models** to match new schema

### **Performance Impact**
- **Schedule migration** during low-traffic periods
- **Monitor query performance** after migration
- **Update application caching** strategies
