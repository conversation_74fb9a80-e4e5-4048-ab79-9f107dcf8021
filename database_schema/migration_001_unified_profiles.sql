-- Migration 001: Create Unified Profiles Table and Role Extensions
-- This migration consolidates user data and eliminates redundancy

-- =====================================================
-- STEP 1: Create Enhanced Profiles Table
-- =====================================================

-- First, let's enhance the existing profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS first_name TEXT,
ADD COLUMN IF NOT EXISTS last_name TEXT,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS address TEXT,
ADD COLUMN IF NOT EXISTS city TEXT,
ADD COLUMN IF NOT EXISTS district TEXT,
ADD COLUMN IF NOT EXISTS postal_code TEXT;

-- Update existing data structure
UPDATE profiles SET 
    first_name = COALESCE(SPLIT_PART(name, ' ', 1), name),
    last_name = CASE 
        WHEN POSITION(' ' IN name) > 0 THEN SUBSTRING(name FROM POSITION(' ' IN name) + 1)
        ELSE ''
    END
WHERE first_name IS NULL;

-- Add constraints
ALTER TABLE profiles 
ADD CONSTRAINT IF NOT EXISTS valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
ADD CONSTRAINT IF NOT EXISTS valid_role CHECK (role IN ('student', 'instructor', 'admin'));

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_auth_user_id ON profiles(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_is_active ON profiles(is_active);

-- =====================================================
-- STEP 2: Create Student Profiles Extension Table
-- =====================================================

CREATE TABLE IF NOT EXISTS student_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Student-specific fields
    current_instructor_id UUID REFERENCES profiles(id),
    program_type TEXT NOT NULL CHECK (program_type IN ('basic', 'premium', 'vip')),
    subscription_expires_at TIMESTAMPTZ,
    remaining_sessions INTEGER DEFAULT 0,
    
    -- Fitness profile
    fitness_level TEXT CHECK (fitness_level IN ('beginner', 'intermediate', 'advanced')),
    fitness_goals TEXT[],
    medical_conditions TEXT,
    dietary_restrictions TEXT[],
    
    -- Progress tracking
    start_weight DECIMAL(5,2),
    current_weight DECIMAL(5,2),
    target_weight DECIMAL(5,2),
    height DECIMAL(5,2),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id),
    CONSTRAINT valid_program_type CHECK (program_type IS NOT NULL)
);

-- =====================================================
-- STEP 3: Create Instructor Profiles Extension Table
-- =====================================================

CREATE TABLE IF NOT EXISTS instructor_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    
    -- Professional info
    specializations TEXT[] DEFAULT '{}',
    certifications TEXT[],
    experience_years INTEGER DEFAULT 0,
    education TEXT,
    
    -- Business info
    gym_affiliation TEXT,
    department TEXT,
    license_number TEXT,
    
    -- Pricing (flexible structure)
    pricing_model JSONB DEFAULT '{}',
    session_price DECIMAL(10,2),
    
    -- Performance metrics
    total_clients INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.0,
    total_reviews INTEGER DEFAULT 0,
    
    -- Social links
    linkedin_url TEXT,
    instagram_url TEXT,
    website_url TEXT,
    
    -- Status
    is_accepting_clients BOOLEAN DEFAULT true,
    verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id),
    CONSTRAINT valid_experience CHECK (experience_years >= 0),
    CONSTRAINT valid_rating CHECK (average_rating >= 0 AND average_rating <= 5)
);

-- =====================================================
-- STEP 4: Create Student-Instructor Assignment Table
-- =====================================================

CREATE TABLE IF NOT EXISTS student_instructor_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL REFERENCES profiles(id),
    instructor_id UUID NOT NULL REFERENCES profiles(id),
    
    -- Assignment details
    program_type TEXT NOT NULL,
    start_date TIMESTAMPTZ DEFAULT NOW(),
    end_date TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    
    -- Progress tracking
    sessions_completed INTEGER DEFAULT 0,
    sessions_total INTEGER,
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(student_id, instructor_id, start_date),
    CONSTRAINT valid_dates CHECK (end_date IS NULL OR end_date > start_date)
);

-- =====================================================
-- STEP 5: Create Updated Reviews Table
-- =====================================================

CREATE TABLE IF NOT EXISTS instructor_reviews_new (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructor_id UUID NOT NULL REFERENCES profiles(id),
    student_id UUID NOT NULL REFERENCES profiles(id),
    
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(instructor_id, student_id)
);

-- =====================================================
-- STEP 6: Create Updated Payments Table
-- =====================================================

CREATE TABLE IF NOT EXISTS payments_new (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User references (unified)
    student_id UUID NOT NULL REFERENCES profiles(id),
    instructor_id UUID NOT NULL REFERENCES profiles(id),
    
    -- Payment details
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'TRY',
    payment_method TEXT,
    transaction_id TEXT,
    
    -- Program details
    program_type TEXT NOT NULL,
    duration_months INTEGER NOT NULL,
    sessions_included INTEGER,
    
    -- Status
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- STEP 7: Enable RLS on New Tables
-- =====================================================

ALTER TABLE student_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE instructor_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_instructor_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE instructor_reviews_new ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments_new ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 8: Create RLS Policies
-- =====================================================

-- Student profiles policies
CREATE POLICY "Users can view own student profile" ON student_profiles
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update own student profile" ON student_profiles
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can insert own student profile" ON student_profiles
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Instructor profiles policies
CREATE POLICY "Users can view own instructor profile" ON instructor_profiles
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update own instructor profile" ON instructor_profiles
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can insert own instructor profile" ON instructor_profiles
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Public can view verified instructors" ON instructor_profiles
    FOR SELECT USING (verification_status = 'verified');

-- Assignment policies
CREATE POLICY "Students can view own assignments" ON student_instructor_assignments
    FOR SELECT USING (student_id = auth.uid());

CREATE POLICY "Instructors can view own assignments" ON student_instructor_assignments
    FOR SELECT USING (instructor_id = auth.uid());

-- Reviews policies
CREATE POLICY "Users can view reviews for instructors" ON instructor_reviews_new
    FOR SELECT USING (true);

CREATE POLICY "Students can create reviews" ON instructor_reviews_new
    FOR INSERT WITH CHECK (student_id = auth.uid());

-- Payments policies
CREATE POLICY "Users can view own payments" ON payments_new
    FOR SELECT USING (student_id = auth.uid() OR instructor_id = auth.uid());

-- =====================================================
-- STEP 9: Create Helper Functions
-- =====================================================

-- Function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_uuid UUID)
RETURNS TEXT AS $$
BEGIN
    RETURN (SELECT role FROM profiles WHERE id = user_uuid);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is instructor
CREATE OR REPLACE FUNCTION is_instructor(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (SELECT role = 'instructor' FROM profiles WHERE id = user_uuid);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is student
CREATE OR REPLACE FUNCTION is_student(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (SELECT role = 'student' FROM profiles WHERE id = user_uuid);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
