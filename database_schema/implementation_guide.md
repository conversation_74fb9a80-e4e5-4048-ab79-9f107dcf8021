# 🚀 **Database Schema Refactor Implementation Guide**

## 📋 **Pre-Migration Checklist**

### ✅ **Backup Requirements**
- [ ] **Full database backup** created and verified
- [ ] **Staging environment** setup with production data copy
- [ ] **Rollback plan** documented and tested
- [ ] **Application downtime** scheduled and communicated

### ✅ **Testing Requirements**
- [ ] **Migration scripts** tested on staging environment
- [ ] **Data integrity** verified after staging migration
- [ ] **Application compatibility** tested with new schema
- [ ] **Performance benchmarks** established

---

## 🔄 **Migration Execution Steps**

### **Phase 1: Schema Creation (Low Risk)**
```bash
# Execute on staging first, then production
psql -h your-supabase-host -d postgres -f migration_001_unified_profiles.sql
```

**Expected Duration:** 2-5 minutes  
**Risk Level:** Low  
**Rollback:** Simple (just drop new tables)

### **Phase 2: Data Migration (Medium Risk)**
```bash
# Execute data migration
psql -h your-supabase-host -d postgres -f migration_002_data_migration.sql
```

**Expected Duration:** 10-30 minutes (depending on data size)  
**Risk Level:** Medium  
**Rollback:** Restore from backup

### **Phase 3: Application Update (High Risk)**
- Update Flutter app to use new schema
- Deploy updated Riverpod providers
- Test all user flows

### **Phase 4: Cleanup (High Risk)**
```bash
# Execute only after thorough testing
psql -h your-supabase-host -d postgres -f migration_003_cleanup.sql
```

**Expected Duration:** 5-10 minutes  
**Risk Level:** High (removes old tables)  
**Rollback:** Restore from backup

---

## 🔧 **Flutter/Riverpod Code Updates**

### **Updated User Model**
```dart
// lib/src/shared/models/user_model.dart
class UserProfile {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phone;
  final String? avatarUrl;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? bio;
  final UserRole role;
  final bool isActive;
  final bool isVerified;
  final Address? address;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Role-specific extensions
  final StudentProfile? studentProfile;
  final InstructorProfile? instructorProfile;
}

enum UserRole { student, instructor, admin }

class StudentProfile {
  final String userId;
  final String? currentInstructorId;
  final ProgramType programType;
  final DateTime? subscriptionExpiresAt;
  final int remainingSessions;
  final FitnessLevel? fitnessLevel;
  final List<String> fitnessGoals;
  final String? medicalConditions;
  final List<String> dietaryRestrictions;
  // ... other fields
}

class InstructorProfile {
  final String userId;
  final List<String> specializations;
  final List<String> certifications;
  final int experienceYears;
  final String? education;
  final String? gymAffiliation;
  final Map<String, double> pricingModel;
  final double? sessionPrice;
  final int totalClients;
  final double averageRating;
  final int totalReviews;
  final bool isAcceptingClients;
  final VerificationStatus verificationStatus;
  // ... other fields
}
```

### **Updated Repository**
```dart
// lib/src/shared/repositories/user_repository.dart
class UserRepository {
  final SupabaseClient _supabase;

  // Get complete user profile with role extensions
  Future<UserProfile?> getUserProfile(String userId) async {
    final response = await _supabase
        .from('profiles')
        .select('''
          *,
          student_profiles(*),
          instructor_profiles(*)
        ''')
        .eq('id', userId)
        .maybeSingle();

    if (response == null) return null;
    return UserProfile.fromJson(response);
  }

  // Get users by role
  Future<List<UserProfile>> getUsersByRole(UserRole role) async {
    final response = await _supabase
        .from('profiles')
        .select('*')
        .eq('role', role.name);

    return response.map((json) => UserProfile.fromJson(json)).toList();
  }

  // Update user profile
  Future<void> updateUserProfile(UserProfile profile) async {
    await _supabase
        .from('profiles')
        .update(profile.toJson())
        .eq('id', profile.id);

    // Update role-specific data
    if (profile.studentProfile != null) {
      await _supabase
          .from('student_profiles')
          .upsert(profile.studentProfile!.toJson());
    }

    if (profile.instructorProfile != null) {
      await _supabase
          .from('instructor_profiles')
          .upsert(profile.instructorProfile!.toJson());
    }
  }
}
```

### **Updated Providers**
```dart
// lib/src/shared/providers/user_provider.dart
final userRepositoryProvider = Provider<UserRepository>((ref) {
  return UserRepository();
});

final currentUserProvider = FutureProvider<UserProfile?>((ref) async {
  final repository = ref.read(userRepositoryProvider);
  final authUser = Supabase.instance.client.auth.currentUser;
  
  if (authUser == null) return null;
  
  return await repository.getUserProfile(authUser.id);
});

final instructorsProvider = FutureProvider<List<UserProfile>>((ref) async {
  final repository = ref.read(userRepositoryProvider);
  return await repository.getUsersByRole(UserRole.instructor);
});

final studentsProvider = FutureProvider<List<UserProfile>>((ref) async {
  final repository = ref.read(userRepositoryProvider);
  return await repository.getUsersByRole(UserRole.student);
});
```

---

## 🧪 **Testing Checklist**

### **Data Integrity Tests**
- [ ] All students migrated correctly
- [ ] All instructors migrated correctly  
- [ ] All relationships preserved
- [ ] No data loss detected
- [ ] Foreign key constraints working

### **Application Tests**
- [ ] User authentication works
- [ ] Profile loading works
- [ ] Role-based UI rendering works
- [ ] Student-instructor assignments work
- [ ] Payment flow works
- [ ] Review system works

### **Performance Tests**
- [ ] Profile queries perform well
- [ ] List queries perform well
- [ ] Complex joins perform well
- [ ] No N+1 query problems

---

## 🔍 **Verification Queries**

### **Check Migration Success**
```sql
-- Verify all data migrated
SELECT 
    'profiles' as table_name, COUNT(*) as count FROM profiles
UNION ALL
SELECT 
    'student_profiles', COUNT(*) FROM student_profiles
UNION ALL
SELECT 
    'instructor_profiles', COUNT(*) FROM instructor_profiles;

-- Check for orphaned records
SELECT COUNT(*) as orphaned_students 
FROM student_profiles sp 
LEFT JOIN profiles p ON p.id = sp.user_id 
WHERE p.id IS NULL;

SELECT COUNT(*) as orphaned_instructors 
FROM instructor_profiles ip 
LEFT JOIN profiles p ON p.id = ip.user_id 
WHERE p.id IS NULL;
```

### **Performance Check**
```sql
-- Test complex query performance
EXPLAIN ANALYZE
SELECT 
    p.first_name,
    p.last_name,
    ip.average_rating,
    ip.total_clients
FROM profiles p
JOIN instructor_profiles ip ON ip.user_id = p.id
WHERE p.role = 'instructor' 
  AND ip.verification_status = 'verified'
  AND ip.is_accepting_clients = true
ORDER BY ip.average_rating DESC
LIMIT 20;
```

---

## ⚠️ **Rollback Procedures**

### **If Migration Fails During Phase 1**
```sql
-- Drop new tables
DROP TABLE IF EXISTS student_profiles CASCADE;
DROP TABLE IF EXISTS instructor_profiles CASCADE;
DROP TABLE IF EXISTS student_instructor_assignments CASCADE;
DROP TABLE IF EXISTS instructor_reviews_new CASCADE;
DROP TABLE IF EXISTS payments_new CASCADE;
```

### **If Migration Fails During Phase 2**
```bash
# Restore from backup
pg_restore -h your-supabase-host -d postgres your-backup-file.dump
```

### **If Issues Found After Phase 3**
```sql
-- Restore old table names
ALTER TABLE payments_backup RENAME TO payments;
ALTER TABLE instructor_reviews_backup RENAME TO instructor_reviews;

-- Re-enable old foreign key constraints
-- (Specific commands depend on your constraints)
```

---

## 📊 **Post-Migration Monitoring**

### **Key Metrics to Monitor**
- Query performance on new tables
- Application error rates
- User authentication success rates
- Data consistency checks
- Storage usage changes

### **Monitoring Queries**
```sql
-- Daily data consistency check
SELECT 
    DATE(created_at) as date,
    COUNT(*) as new_profiles
FROM profiles 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Performance monitoring
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch
FROM pg_stat_user_tables 
WHERE tablename IN ('profiles', 'student_profiles', 'instructor_profiles');
```

---

## 🎯 **Success Criteria**

### **Migration is Successful When:**
- [ ] All data migrated without loss
- [ ] Application functions normally
- [ ] Performance is maintained or improved
- [ ] No critical bugs reported
- [ ] User experience is unchanged or better

### **Ready for Cleanup When:**
- [ ] 48+ hours of stable operation
- [ ] All stakeholders approve
- [ ] Backup retention policy satisfied
- [ ] Monitoring shows healthy metrics

---

## 📞 **Emergency Contacts**

- **Database Admin:** [Your DBA contact]
- **DevOps Team:** [Your DevOps contact]  
- **Product Owner:** [Your PO contact]
- **On-call Engineer:** [Your on-call contact]

---

**Remember:** This is a major schema change. Take your time, test thoroughly, and don't hesitate to rollback if anything seems wrong!
